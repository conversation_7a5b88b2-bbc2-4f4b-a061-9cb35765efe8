import json
import os

from blitzy_utils.enums import BackpropChangeMode

EVENT_DATA = json.dumps({
    'repo_name': 'hamlet',
    'repo_id': 'repo_id',
    'branch_id': 'main',
    'company_id': 'Symphony42',
    'user_id': '4428a4b5-0dd0-4734-8b95-f5dd81b702a4',
    'head_commit_hash': '6c75f2228c11b50427f1d64aa78f729f9c48f1e8',
    'document_mode': BackpropChangeMode.UPDATE.value
})
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["PROJECT_ID"] = 'blitzy-os-dev'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-os-internal'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'  # dummy value
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
os.environ["VOYAGE_API_KEY"] = "pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE"
os.environ["GEMINI_API_KET"] = "AIzaSyAA_WvkItpwfiL3D8czRWsT5dbA2iXmF_I"
os.environ["SERVICE_URL_ADMIN"] = "https://archie-service-admin-464705070478.us-central1.run.app"

NEO4J_SERVER = "neo4j://34.66.114.166:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "bavjoz-pamciB-6vifce"
os.environ["NEO4J_SERVER"] = NEO4J_SERVER
os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD
os.environ["MARKDOWN_SERVER"] = "https://archie-service-markdown-464705070478.us-central1.run.app/v1/mermaid/validate"

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = DEV_GITHUB_SECRET_SERVER

os.environ["LANGCHAIN_TRACING_V2"] = ""
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = ""
