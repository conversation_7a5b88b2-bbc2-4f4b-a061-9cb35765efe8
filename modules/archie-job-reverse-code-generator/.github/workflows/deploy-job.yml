name: Deploy Cloud Run Job

on:
  push:
    branches:
      - main

concurrency:
  group: dev-deployments
  cancel-in-progress: true

jobs:
  deploy-linux:
    runs-on: ubuntu-latest
    environment: dev
    permissions:
      contents: 'read'
      id-token: 'write'

    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID_DEV }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-job-reverse-code-generator
      IMAGE_TAG: latest
      SERVICE_NAME: archie-job-reverse-code-generator

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_CREDENTIALS_DEV }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: '${{ vars.PROJECT_ID_DEV }}'

    - name: Configure Docker for GCP Artifactory
      run: |
        gcloud auth configure-docker $ARTIFACTORY_DOMAIN

    - name: Build Docker image
      run: |
        make

    - name: Install deployment utils
      run: make install-deployment-utils

    - name: Tag Docker image
      run: |
        docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

    - name: Push Docker image
      run: |
        echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
        echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

    - name: 'Use gcloud CLI'
      run: 'gcloud info'

    - name: Deploy Cloud Run job
      run: |
         gcloud --quiet beta run jobs deploy reverse-code-generator \
          --image $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }} \
          --region ${{ vars.REGION }} \
          --vpc-connector ${{ vars.VPC_CONNECTOR_DEV }} \
          --vpc-egress ${{ vars.VPC_EGRESS }} \
          --service-account ${{ vars.SERVICE_ACCOUNT_DEV }} \
          --memory ${{ vars.MEMORY }} \
          --cpu ${{ vars.CPU }} \
          --task-timeout ${{ vars.BETA_TIMEOUT }} \
          --max-retries ${{ vars.MAX_RETRIES }} \
          --set-env-vars "\
            PROJECT_ID=${{ vars.PROJECT_ID_DEV }},\
            GCS_BUCKET_NAME=${{ vars.GCS_BUCKET_NAME_DEV }},\
            SERVICE_NAME=$SERVICE_NAME,\
            PRIVATE_BLOB_NAME=${{ vars.PRIVATE_BLOB_NAME }},\
            PLATFORM_EVENTS_TOPIC=${{ vars.PLATFORM_EVENTS_TOPIC }},\
            ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY_DEV }},\
            OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY_DEV }},\
            VOYAGE_API_KEY=${{ secrets.VOYAGE_API_KEY_DEV }},\
            GOOGLE_API_KEY=${{ secrets.GOOGLE_API_KEY_DEV }},\
            GITHUB_SECRET_SERVER=${{ vars.SECRET_SERVER_DEV }},\
            SERVICE_URL_GITHUB=${{ vars.SERVICE_URL_GITHUB_DEV }},\
            SERVICE_URL_ADMIN=${{ vars.SERVICE_URL_ADMIN_DEV }},\
            NEO4J_SERVER=${{ vars.NEO4J_SERVER_DEV }},\
            NEO4J_USERNAME=${{ secrets.NEO4J_USERNAME_DEV }},\
            NEO4J_PASSWORD=${{ secrets.NEO4J_PASSWORD_DEV }},\
            LANGCHAIN_TRACING_V2=${{ vars.LANGCHAIN_TRACING_V2 }},\
            LANGCHAIN_ENDPOINT=${{ vars.LANGCHAIN_ENDPOINT }},\
            LANGCHAIN_API_KEY=${{ secrets.LANGCHAIN_API_KEY_DEV }},\
            LANGCHAIN_PROJECT=${{ vars.LANGCHAIN_PROJECT }},\
            TOKENIZERS_PARALLELISM=false"
    
    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Build Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* ${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>

  deploy-windows:
    runs-on: windows-latest
    environment: dev
    permissions:
      contents: 'read'
      id-token: 'write'

    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID_DEV }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-job-reverse-code-generator-windows
      IMAGE_TAG: latest
      SERVICE_NAME: archie-job-reverse-code-generator

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_CREDENTIALS_DEV }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: '${{ vars.PROJECT_ID_DEV }}'

      - name: Configure Docker for GCP Artifactory
        shell: powershell
        run: |
          gcloud auth configure-docker $env:ARTIFACTORY_DOMAIN

      - name: Create credentials file for Docker build
        shell: powershell
        run: |
          $credPath = "$env:TEMP\gcp-credentials.json"
          '${{ secrets.GCP_CREDENTIALS_DEV }}' | Out-File -FilePath $credPath -Encoding UTF8
          echo "GOOGLE_APPLICATION_CREDENTIALS=$credPath" >> $env:GITHUB_ENV

      - name: Build Windows Docker image
        shell: powershell
        run: |
          # Read credentials file
          $credPath = $env:GOOGLE_APPLICATION_CREDENTIALS
          Write-Host "Reading credentials from: $credPath"

          if (-not (Test-Path $credPath)) {
              Write-Error "Credentials file not found at: $credPath"
              exit 1
          }

          # Read content and verify it's valid JSON
          $credContent = Get-Content $credPath -Raw
          Write-Host "Credentials file size: $($credContent.Length) characters"

          try {
              $null = $credContent | ConvertFrom-Json
              Write-Host "Credentials file is valid JSON"
          } catch {
              Write-Error "Invalid JSON in credentials file"
              exit 1
          }

          # Base64 encode the credentials
          $credBytes = [System.Text.Encoding]::UTF8.GetBytes($credContent)
          $credBase64 = [Convert]::ToBase64String($credBytes)
          Write-Host "Base64 encoded credentials length: $($credBase64.Length) characters"

          # Build with base64 encoded credentials
          docker build `
            --build-arg GOOGLE_CREDENTIALS_BASE64="$credBase64" `
            -f Dockerfile.windows `
            -t "$env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${env:IMAGE_TAG}" `
            .

      - name: Tag Windows Docker image
        shell: powershell
        run: |
          docker tag "$env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${env:IMAGE_TAG}" "$env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${{ github.sha }}"

      - name: Push Windows Docker image
        shell: powershell
        run: |
          Write-Host "Pushing $env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${env:IMAGE_TAG}"
          Write-Host "Pushing image with SHA $env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${{ github.sha }}"
          docker push "$env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${{ github.sha }}"
          docker push "$env:ARTIFACTORY_IMAGE_DOMAIN/${env:IMAGE_NAME}:${env:IMAGE_TAG}"

      - name: Clean up credentials file
        if: always()
        shell: powershell
        run: |
          if (Test-Path "$env:TEMP\gcp-credentials.json") {
            Remove-Item "$env:TEMP\gcp-credentials.json" -Force
          }
