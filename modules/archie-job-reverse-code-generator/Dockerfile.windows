# Use Windows Server Core with Python 3.12
FROM winamd64/python:3.12-windowsservercore-ltsc2022

# Set PowerShell as the default shell
SHELL ["powershell", "-Command", "$ErrorActionPreference = 'Stop'; $ProgressPreference = 'SilentlyContinue';"]

# Install Chocolatey for package management
RUN Set-ExecutionPolicy Bypass -Scope Process -Force; \
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; \
    iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

# Install Node.js 22.x and Git
RUN choco install -y nodejs --version=22.16.0
RUN choco install -y git

# Install TypeScript globally
RUN npm install -g typescript

# Set working directory
WORKDIR /app

# Install keyring for artifact registry auth
RUN pip install keyrings.google-artifactregistry-auth

# Copy requirements.txt first
COPY requirements.txt .

# Use build argument for base64 encoded credentials
ARG GOOGLE_CREDENTIALS_BASE64

# Install Python dependencies using decoded credentials
RUN if ($env:GOOGLE_CREDENTIALS_BASE64) { \
        Write-Host 'Decoding and setting up Google credentials'; \
        $decodedBytes = [Convert]::FromBase64String($env:GOOGLE_CREDENTIALS_BASE64); \
        $decodedText = [System.Text.Encoding]::UTF8.GetString($decodedBytes); \
        # Debug: Check if credentials were decoded properly
        Write-Host "Decoded credentials length: $($decodedText.Length) characters"; \
        if ($decodedText.Length -eq 0) { \
            Write-Error "Credentials were not decoded properly - empty string"; \
            exit 1; \
        } \
        # Create directory and write file
        New-Item -ItemType Directory -Force -Path C:\gcp | Out-Null; \
        [System.IO.File]::WriteAllText('C:\gcp\credentials.json', $decodedText); \
        # Verify the file was written correctly
        if (Test-Path 'C:\gcp\credentials.json') { \
            $fileSize = (Get-Item 'C:\gcp\credentials.json').Length; \
            Write-Host "Credentials file created, size: $fileSize bytes"; \
            # Try to parse as JSON to verify
            try { \
                $null = Get-Content 'C:\gcp\credentials.json' -Raw | ConvertFrom-Json; \
                Write-Host "Credentials file is valid JSON"; \
            } catch { \
                Write-Error "Credentials file is not valid JSON: $_"; \
                Get-Content 'C:\gcp\credentials.json' -Head 5; \
                exit 1; \
            } \
        } else { \
            Write-Error "Failed to create credentials file"; \
            exit 1; \
        } \
        $env:GOOGLE_APPLICATION_CREDENTIALS = 'C:\gcp\credentials.json'; \
        Write-Host "Installing packages with Artifact Registry authentication"; \
        pip install -r requirements.txt; \
        Remove-Item -Path C:\gcp -Recurse -Force; \
    } else { \
        Write-Host 'No credentials provided, installing public packages only'; \
        pip install google-cloud-pubsub==2.29.0 google-cloud-storage==2.19.0; \
    }

# Copy application code
COPY . .

# Set the entrypoint to Python
ENTRYPOINT ["python"]
CMD ["main.py"]