# Git files
.git
.gitignore
/downloads/
test_github.py

# CI/CD files
.github/
Jenkinsfile
.gitlab-ci.yml

# Documentation
README.md
docs/
*.md

# Development files
.env
.env.*
*.log
*.pid
*.seed
*.pid.lock

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Python files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.pytest_cache/
.coverage
htmlcov/
.tox/
.mypy_cache/
.dmypy.json
dmypy.json

# Credentials - IMPORTANT
gcp-creds.json
*-credentials.json
*.key
*.pem
service-account*.json

# Build artifacts
build/
dist/
*.egg-info/

# OS files
.DS_Store
Thumbs.db

# Docker files
Dockerfile*
docker-compose*.yml

# Temporary files
*.tmp
*.temp
temp/
tmp/
service-key-*.json
/blitzy/
