from typing import TypedDict, List, Dict, Any

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder


class ReverseMapperState(TypedDict):
    branch_id: str
    current_folder_path: str
    current_folder_structure: str
    dest_branch_id: str
    dest_branch_name: str
    dest_repo_id: str
    dest_repo_name: str
    excluded_folders: List[Dict[str, Any]]
    excluded_files: List[Dict[str, Any]]
    file_mapping: Dict[str, List[Dict[str, Any]]]
    file_schemas: Dict[str, Dict[str, Any]]
    graph_builder: CodeGraphBuilder
    head_commit_hash: str
    is_new_dest_repo: bool
    mode: str
    old_folders_list: List[str]
    pending_folders: List[str]
    pending_folder_details: Dict[str, Dict[str, Any]]
    repo_id: str
    repo_name: str
    resume: bool
    short_repo_structure: Dict[str, List[str]]
    tech_spec_first_n: str
    tech_spec_parsed: Dict[str, str]
    user_id: str
    visited_folders: List[str]
    git_project_repo_id: str


def get_state(state: ReverseMapperState):
    return {
        "branch_id": state["branch_id"],
        "current_folder_path": state["current_folder_path"],
        "current_folder_structure": state["current_folder_structure"],
        "dest_branch_id": state["dest_branch_id"],
        "dest_branch_name": state["dest_branch_name"],
        "dest_repo_id": state["dest_repo_id"],
        "dest_repo_name": state["dest_repo_name"],
        "excluded_files": state["excluded_files"],
        "excluded_folders": state["excluded_folders"],
        "file_mapping": state["file_mapping"],
        "file_schemas": state["file_schemas"],
        "graph_builder": state["graph_builder"],
        "head_commit_hash": state["head_commit_hash"],
        "is_new_dest_repo": state["is_new_dest_repo"],
        "mode": state["mode"],
        "old_folders_list": state["old_folders_list"],
        "pending_folders": state["pending_folders"],
        "pending_folder_details": state["pending_folder_details"],
        "repo_id": state["repo_id"],
        "repo_name": state["repo_name"],
        "resume": state["resume"],
        "short_repo_structure": state["short_repo_structure"],
        "tech_spec_first_n": state["tech_spec_first_n"],
        "tech_spec_parsed": state["tech_spec_parsed"],
        "user_id": state["user_id"],
        "visited_folders": state["visited_folders"],
        "git_project_repo_id": state["git_project_repo_id"]
    }
