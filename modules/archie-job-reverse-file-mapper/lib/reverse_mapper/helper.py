import json
import gc
from typing import Dict, Any, Literal, List, Tuple

from blitzy_utils.common import download_text_file_from_gcs_using_admin_service, upload_text_to_gcs_using_admin_service
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.prebuilt import ToolNode
from anthropic import BadRequest<PERSON><PERSON>r as AnthropicBadRequestError
from thefuzz import process

from blitzy_utils.logger import logger
from blitzy_utils.enums import BackpropCommand
from blitzy_platform_shared.common.utils import get_response_content, get_formatted_tool_result_messages, archie_exponential_retry, \
    format_messages, clean_path, process_tool_call, process_messages_with_tool_call
from blitzy_platform_shared.common.tools import get_file_contents, ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION
from blitzy_platform_shared.common.consts import RETRY<PERSON>LE_EXCEPTIONS, SUP<PERSON>EMENTARY_RETRYABLE_EXCEPTIONS
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.code_graph.tools import get_folder_contents, search_files, search_folders, get_file_summary
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import SUMMARY_OF_CHANGES_INPUT, \
    TECH_SPEC_SECTIONS_INPUT
from blitzy_platform_shared.code_generation.models import FileOrFolderStatus

from .state import ReverseMapperState, get_state
from .models import RepositoryCreatedFolderContents, RepositoryUpdatedFolderContents
from .prompts import NRS_SYSTEM_PROMPT_TEMPLATE, NRS_PERSONA_PROMPTLET, COMMON_INPUTS_PROMPTLET, NRS_RULES_PROMPTLET, \
    FOLDER_CONTEXT_INPUT, DEST_REPO_NAME_INPUT, FOLDER_DETAILS_INPUT, URS_SYSTEM_PROMPT_TEMPLATE, \
    STRUCTURE_SYSTEM_PROMPT, STRUCTURE_INPUT, OUTPUT_SCHEMA_INPUT, URS_RULES_PROMPTLET

rfm_node_tools = [
    get_tech_spec_section,
    get_file_contents,
    get_file_summary,
    get_folder_contents,
    search_folders,
    search_files
]
rfm_tools = rfm_node_tools + [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
rfm_tool_node = ToolNode(rfm_node_tools)


class ReverseMapperHelper:
    def __init__(
            self,
            repo_structure_llm: BaseChatModel,
            structure_llm: BaseChatModel,
            fallback_llms: List[BaseChatModel],
            job_metadata: Dict[str, Any],
            storage_client,
            bucket_name: str,
            blob_name: str,
            graph_builder: CodeGraphBuilder,
            file_mapping_filename: str,
            short_repo_structure_filename: str,
            state_metadata_filename: str,
            github_server: str
    ):
        self.repo_structure_llm = repo_structure_llm
        self.structure_llm = structure_llm
        self.fallback_llms = fallback_llms
        self.job_metadata = job_metadata
        self.storage_client = storage_client
        self.graph_builder = graph_builder
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.file_mapping_filename = file_mapping_filename
        self.short_repo_structure_filename = short_repo_structure_filename
        self.state_metadata_filename = state_metadata_filename
        self.github_server = github_server

        self.company_id = self.job_metadata["company_id"]
        self.team_id = self.job_metadata["team_id"]
        self.user_id = self.job_metadata["user_id"]
        self.project_id = self.job_metadata["project_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.repo_id = self.job_metadata["repo_id"]
        self.branch_name = self.job_metadata["branch_name"]
        self.branch_id = self.job_metadata["branch_id"]
        self.head_commit_hash = self.job_metadata["head_commit_hash"]
        self.dest_repo_name = self.job_metadata["dest_repo_name"]
        self.dest_repo_id = self.job_metadata["dest_repo_id"]
        self.dest_branch_id = self.job_metadata["dest_branch_id"]
        self.dest_branch_name = self.job_metadata["dest_branch_name"]
        self.is_new_dest_repo = self.job_metadata["is_new_dest_repo"]
        self.git_project_repo_id = self.job_metadata["git_project_repo_id"]

        self.generator = self.create_graph()

    def create_graph(self) -> StateGraph:
        # Define the graph
        generator = StateGraph(ReverseMapperState)

        # Add nodes
        generator.add_node("setup", self.setup)
        generator.add_node("generate_new_folder_structure", self.generate_new_folder_structure)
        generator.add_node("format_new_folder_structure", self.format_new_folder_structure)
        generator.add_node("generate_updated_folder_structure", self.generate_updated_folder_structure)
        generator.add_node("format_updated_folder_structure", self.format_updated_folder_structure)

        generator.add_conditional_edges(
            "setup",
            self.setup_router,
            {
                "add_feature": "generate_updated_folder_structure",
                "refactor_create_repo": "generate_new_folder_structure",
                "refactor_existing_repo": "generate_updated_folder_structure"
            }
        )

        generator.add_conditional_edges(
            "format_new_folder_structure",
            self.folder_router,
            {
                "continue": "generate_new_folder_structure",
                "end": END
            }
        )

        generator.add_conditional_edges(
            "format_updated_folder_structure",
            self.folder_router,
            {
                "continue": "generate_updated_folder_structure",
                "end": END
            }
        )

        # Set the entry point
        generator.add_edge(START, "setup")
        generator.add_edge("generate_new_folder_structure", "format_new_folder_structure")
        generator.add_edge("generate_updated_folder_structure", "format_updated_folder_structure")

        return generator

    def setup(self, state: ReverseMapperState) -> Dict[str, Any]:
        state["graph_builder"] = self.graph_builder
        state["repo_id"] = self.repo_id
        state["repo_name"] = self.repo_name
        state["branch_id"] = self.branch_id
        state["head_commit_hash"] = self.head_commit_hash
        state["team_id"] = self.team_id
        state["user_id"] = self.user_id
        state["dest_repo_name"] = self.dest_repo_name
        state["dest_branch_id"] = self.dest_branch_id
        state["dest_branch_name"] = self.dest_branch_name
        state["dest_repo_id"] = self.dest_repo_id
        state["is_new_dest_repo"] = self.is_new_dest_repo
        state["git_project_repo_id"] = self.git_project_repo_id

        if state["resume"]:
            # Restore state
            logger.info('Attempting to resume state')
            try:
                file_mapping_filepath = f"{self.blob_name}/{self.file_mapping_filename}"
                state["file_mapping"] = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=file_mapping_filepath,
                    company_id=self.company_id,
                ))

                short_repo_structure_filepath = f"{self.blob_name}/{self.short_repo_structure_filename}"
                state["short_repo_structure"] = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=short_repo_structure_filepath,
                    company_id=self.company_id,
                ))

                state_metadata_filepath = f"{self.blob_name}/{self.state_metadata_filename}"
                state_metadata = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=state_metadata_filepath,
                    company_id=self.company_id,
                ))
                logger.info(f'Restored state: {state_metadata}')
            except Exception as e:
                logger.warning(f'Failed to resume state, starting afresh')
                state["resume"] = False
                return self.setup(state=state)
            state["pending_folders"] = state_metadata["pending_folders"]
            state["pending_folder_details"] = state_metadata["pending_folder_details"]
            state["visited_folders"] = state_metadata["visited_folders"]
            state["file_mapping"] = state_metadata["file_mapping"]
            state["file_schemas"] = state_metadata["file_schemas"]
            state["short_repo_structure"] = state_metadata["short_repo_structure"]
            state["excluded_folders"] = state_metadata["excluded_folders"]
            state["excluded_files"] = state_metadata["excluded_files"]
            state["current_folder_path"] = state_metadata["current_folder_path"]
            state["current_folder_structure"] = state_metadata["current_folder_structure"]
            state["old_folders_list"] = state_metadata["old_folders_list"]
        else:
            # Reset state, begin with root folder
            state["pending_folders"] = [""]
            state["pending_folder_details"] = {}
            state["visited_folders"] = []
            state["file_mapping"] = {}
            state["file_schemas"] = {}
            state["short_repo_structure"] = {}
            state["excluded_folders"] = []
            state["excluded_files"] = []
            state["current_folder_path"] = ""
            state["current_folder_structure"] = ""
            state["old_folders_list"] = []

        return get_state(state=state)

    def setup_router(self, state: ReverseMapperState) -> Literal["add_feature", "refactor_create_repo", "refactor_existing_repo"]:
        if state["mode"] in [BackpropCommand.REFACTOR_CODE.value, BackpropCommand.CUSTOM.value]:
            if state["is_new_dest_repo"]:
                return "refactor_create_repo"
            else:
                return "refactor_existing_repo"
        else:
            return "add_feature"

    def folder_router(self, state: ReverseMapperState) -> Literal["continue", "end"]:
        if len(state["pending_folders"]) > 0:
            return "continue"
        else:
            return "end"

    def setup_add_feature(self, state: ReverseMapperState) -> Dict[str, Any]:
        return get_state(state=state)

    @archie_exponential_retry()
    def generate_new_folder_structure(self, state: ReverseMapperState) -> Dict[str, Any]:
        # Run manual garbage collection
        gc.collect()

        state["current_folder_path"] = state["pending_folders"].pop(0)
        folder_path = state["current_folder_path"]
        state["visited_folders"].append(folder_path)
        logger.info(f'generating new repo structure for folder: {"root" if folder_path == "" else folder_path}')

        assigned_files, assigned_folders, old_folders = self.get_existing_files_and_folders(state=state)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": NRS_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=NRS_PERSONA_PROMPTLET,
                        inputs=COMMON_INPUTS_PROMPTLET,
                        rules=f"{NRS_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FOLDER_CONTEXT_INPUT.format(
                        old_paths={"" if folder_path == "" else json.dumps(old_folders)},
                        assigned_files=json.dumps(assigned_files),
                        assigned_folders=json.dumps(assigned_folders)
                    ),
                },
                {
                    "type": "text",
                    "text": SUMMARY_OF_CHANGES_INPUT.format(
                        summary=state["tech_spec_first_n"]
                    )
                },
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    )
                },
                {
                    "type": "text",
                    "text": DEST_REPO_NAME_INPUT.format(
                        dest_repo_name=state["dest_repo_name"]
                    )
                },
                {
                    "type": "text",
                    "text": OUTPUT_SCHEMA_INPUT.format(
                        schema=RepositoryCreatedFolderContents.model_json_schema()
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": FOLDER_DETAILS_INPUT.format(
                        folder_path=folder_path,
                        folder_details=json.dumps(state["pending_folder_details"].get(folder_path, {}))
                    )
                }
            ])
        ]

        try:
            orig_messages = messages.copy()
            llm = self.repo_structure_llm
            retry_count = 0
            while True:
                messages = orig_messages.copy()
                try:
                    response: AIMessage = llm.invoke(messages)

                    # logger.info(response)

                    while len(response.tool_calls):
                        # fill in the value of local variables
                        messages.append(response)
                        total_tokens = response.usage_metadata["total_tokens"]
                        # logger.info(total_tokens)
                        tool_calls = response.tool_calls
                        tools_config = {
                            "file_mapping": state["file_mapping"],
                            "tech_spec_parsed": state["tech_spec_parsed"],
                            "company_id": self.company_id,
                            "repo_id": self.repo_id,
                            "repo_name": self.repo_name,
                            "branch_id": self.branch_id,
                            "is_new_dest_repo": self.is_new_dest_repo,
                            "user_id": self.user_id,
                            "head_commit_hash": self.head_commit_hash,
                            "graph_builder": self.graph_builder,
                            "github_server": self.github_server,
                            "git_project_repo_id": self.git_project_repo_id,
                        }
                        for tool_call in tool_calls:
                            tool_message = process_tool_call(
                                tool_call=tool_call,
                                tools_list=rfm_node_tools,
                                tools_config=tools_config
                            )
                            messages = process_messages_with_tool_call(
                                tool_message=tool_message,
                                messages=messages,
                                total_tokens_before_tool=total_tokens,
                                llm=llm
                            )
                        # logger.info(f'sending tool response back to llm for file: {file_path}')
                        response: AIMessage = llm.invoke(messages)

                    content = get_response_content(response=response)

                    # logger.info(content)

                    state["current_folder_structure"] = content
                    break
                except AnthropicBadRequestError as e:
                    logger.warning(f'Anthropic bad request error, trying with fallback index {retry_count}: {e}')
                    if retry_count < len(self.fallback_llms):
                        llm = self.fallback_llms[retry_count]
                        retry_count += 1
                    else:
                        logger.error(f'Anthropic bad request error, all fallback llms failed: {e}')
                        raise e
        except RETRYABLE_EXCEPTIONS as e:
            logger.warning(f'encountered error, retrying: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise
        except Exception as e:
            logger.warning(f'encountered error, failing job: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise

        # logger.info(f'current folder structure: {state["current_folder_structure"]}')

        logger.info(
            f'finished generating new repo structure for folder: {"root" if state["current_folder_path"] == "" else state["current_folder_path"]}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    def format_new_folder_structure(self, state: ReverseMapperState) -> Dict[str, Any]:
        folder_path = state["current_folder_path"]
        logger.info(f'formatting new repo structure for folder: {"root" if folder_path == "" else folder_path}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": STRUCTURE_SYSTEM_PROMPT
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": STRUCTURE_INPUT.format(
                        assigned_output=state["current_folder_structure"]
                    )
                }
            ])
        ]

        structured_llm = self.structure_llm.with_structured_output(RepositoryCreatedFolderContents)

        try:
            response: AIMessage = structured_llm.invoke(messages)
        except (RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS) as e:
            logger.warning(f'encountered error, retrying: {e}')
            raise
        except Exception as e:
            logger.warning(f'encountered error, failing job: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise

        response_dict: RepositoryCreatedFolderContents = response.model_dump()

        # logger.info(response_dict)

        self.process_created_response(state=state, response_dict=response_dict)

        # logger.info(f'final repo structure: {state["file_mapping"]}')

        logger.info(
            f'finished formatting new repo structure for folder: {"root" if state["current_folder_path"] == "" else state["current_folder_path"]}')

        self.upload_state(state=state)

        return get_state(state=state)

    def upload_state(self, state: ReverseMapperState) -> None:
        destination_file_mapping_filepath = f"{self.blob_name}/{self.file_mapping_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_file_mapping_filepath,
            company_id=self.company_id,
            data=json.dumps(state["file_mapping"])
        )

        destination_short_repo_structure_filepath = f"{self.blob_name}/{self.short_repo_structure_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_short_repo_structure_filepath,
            company_id=self.company_id,
            data=json.dumps(state["short_repo_structure"])
        )

        state_metadata = {
            "pending_folders": state["pending_folders"],
            "pending_folder_details": state["pending_folder_details"],
            "visited_folders": state["visited_folders"],
            "file_mapping": state["file_mapping"],
            "file_schemas": state["file_schemas"],
            "short_repo_structure": state["short_repo_structure"],
            "excluded_folders": state["excluded_folders"],
            "excluded_files": state["excluded_files"],
            "current_folder_path": state["current_folder_path"],
            "current_folder_structure": state["current_folder_structure"],
            "old_folders_list": state["old_folders_list"]
        }
        destination_state_metadata_filename_filepath = f"{self.blob_name}/{self.state_metadata_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_state_metadata_filename_filepath,
            company_id=self.company_id,
            data=json.dumps(state_metadata)
        )

    def get_existing_files_and_folders(self, state: ReverseMapperState) -> Tuple[List[str], List[str], List[str]]:
        assigned_files = []
        assigned_folders = []
        old_folders = []
        folder_path = state["current_folder_path"]
        if folder_path == "":
            logger.info(f'Retrieving root folder contents')
            folder_info = self.graph_builder.get_folder_contents(
                folder_path=folder_path,
                company_id=self.company_id,
                repo_id=self.repo_id,
                branch_id=self.branch_id
            )
            for child in folder_info["children"]:
                if child["type"] == "file":
                    file = {
                        "type": "file",
                        "path": child["path"]
                    }
                    assigned_files.append(file)
                elif child["type"] == "folder":
                    assigned_folders.append(child)
        else:
            folder_details = state["pending_folder_details"].get(folder_path, {})
            old_folders = folder_details.get("source_folders", [])
            if len(old_folders):
                logger.info(f'using old folder paths as assigned paths: {old_folders}')
                for old_folder_path in old_folders:
                    logger.info(f'retrieving contents of old folder {old_folder_path}')
                    if not len(state["old_folders_list"]):
                        state["old_folders_list"] = self.graph_builder.get_all_folder_paths(
                            company_id=self.company_id,
                            repo_id=self.repo_id,
                            branch_id=self.branch_id
                        )
                    if old_folder_path not in state["old_folders_list"]:
                        fuzzy_path_choices = process.extract(
                            query=old_folder_path,
                            choices=state["old_folders_list"],
                            limit=1
                        )
                        if len(fuzzy_path_choices) and fuzzy_path_choices[0]:
                            old_folder_path = fuzzy_path_choices[0][0]
                            logger.warning(f'fuzzy matched folder path: {old_folder_path}')
                    if old_folder_path == 'root ("")':
                        old_folder_path = ""
                    folder_info = self.graph_builder.get_folder_contents(
                        folder_path=old_folder_path,
                        company_id=self.company_id,
                        repo_id=self.repo_id,
                        branch_id=self.branch_id
                    )
                    if not folder_info:
                        logger.warning(f"Attempted to retrieve contents from invalid path, skipping: {old_folder_path}")
                        continue
                    else:
                        for child in folder_info["children"]:
                            if child["type"] == "file":
                                file = {
                                    "type": "file",
                                    "path": child["path"]
                                }
                                assigned_files.append(file)
                            elif child["type"] == "folder":
                                assigned_folders.append(child)
            else:
                logger.warning(f'No existing folder context available for folder: {folder_path}')
        return assigned_files, assigned_folders, old_folders

    def process_created_response(self, state: ReverseMapperState, response_dict: RepositoryCreatedFolderContents):
        for folder in reversed(response_dict["new_folders"]):
            folder_path = clean_path(folder["dest_path"])
            if not self.is_valid_child_path(state=state, path=folder_path):
                logger.warning(f"Skipping folder with invalid path: {folder_path}")
                continue
            if folder_path not in state["visited_folders"] and folder_path not in state["pending_folders"]:
                logger.info(f'adding new pending folder: {folder_path}')
                state["pending_folders"].insert(0, folder_path)
                state["pending_folder_details"][folder_path] = folder
            else:
                logger.warning(f'folder path already visited or pending: {folder_path}')

        for folder in reversed(response_dict["excluded_folders"]):
            folder_path = clean_path(folder["dest_path"])
            if not self.is_valid_child_path(state=state, path=folder_path):
                logger.warning(f"Skipping excluded folder with invalid path: {folder_path}")
                continue
            if folder_path not in state["excluded_folders"]:
                logger.info(f'adding new excluded folder: {folder_path}')
                state["excluded_folders"].append(folder)
            else:
                logger.warning(f'folder path already excluded: {folder_path}')

        for file in reversed(response_dict["excluded_files"]):
            file_path = clean_path(file["dest_path"])
            if not self.is_valid_child_path(state=state, path=file_path):
                logger.warning(f"Skipping excluded file with invalid path: {file_path}")
                continue
            if file_path not in state["excluded_files"]:
                logger.info(f'adding new excluded file: {file_path}')
                state["excluded_files"].append(file)
            else:
                logger.warning(f'file path already excluded: {file_path}')

        for file in reversed(response_dict["new_files"]):
            file_path = clean_path(file["dest_path"])
            if not self.is_valid_child_path(state=state, path=file_path):
                logger.warning(f"Skipping new file with invalid path: {file_path}")
                continue

            if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
                logger.warning(f"Skipping invalid new file: {file_path}")
                continue

            current_files_list = state["short_repo_structure"].get(state["current_folder_path"], [])
            if file_path not in current_files_list:
                logger.info(f'adding new file path to repo structure: {file_path}')
                file["status"] = FileOrFolderStatus.CREATED.value
                self.add_file_to_state(state=state, file=file, file_path=file_path)
            else:
                logger.warning(f'file path already exists in repo structure: {file_path}')

    def is_valid_child_path(self, state: ReverseMapperState, path: str) -> bool:
        if not path:
            return False

        path_levels = len(path.split('/'))
        if state["current_folder_path"] == "":
            return path_levels == 1

        current_folder_levels = len(state["current_folder_path"].split('/'))
        return path_levels == (current_folder_levels + 1)

    @archie_exponential_retry()
    def generate_updated_folder_structure(self, state: ReverseMapperState) -> Dict[str, Any]:
        # Run manual garbage collection
        gc.collect()

        state["current_folder_path"] = state["pending_folders"].pop(0)
        folder_path = state["current_folder_path"]
        state["visited_folders"].append(folder_path)
        logger.info(f'generating updated repo structure for folder: {"root" if folder_path == "" else folder_path}')

        assigned_files, assigned_folders, old_folders = self.get_existing_files_and_folders(state=state)

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": URS_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=NRS_PERSONA_PROMPTLET,
                        inputs=COMMON_INPUTS_PROMPTLET,
                        rules=f"{URS_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": FOLDER_CONTEXT_INPUT.format(
                        old_paths={"" if folder_path == "" else json.dumps(old_folders)},
                        assigned_files=json.dumps(assigned_files),
                        assigned_folders=json.dumps(assigned_folders)
                    )
                },
                {
                    "type": "text",
                    "text": SUMMARY_OF_CHANGES_INPUT.format(
                        summary=state["tech_spec_first_n"]
                    )
                },
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    )
                },
                {
                    "type": "text",
                    "text": DEST_REPO_NAME_INPUT.format(
                        dest_repo_name=state["dest_repo_name"]
                    )
                },
                {
                    "type": "text",
                    "text": OUTPUT_SCHEMA_INPUT.format(
                        schema=RepositoryUpdatedFolderContents.model_json_schema()
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": FOLDER_DETAILS_INPUT.format(
                        folder_path=folder_path,
                        folder_details=json.dumps(state["pending_folder_details"].get(folder_path, {}))
                    )
                }
            ])
        ]

        try:
            orig_messages = messages.copy()
            llm = self.repo_structure_llm
            retry_count = 0
            while True:
                messages = orig_messages.copy()
                try:
                    response: AIMessage = llm.invoke(messages)

                    while len(response.tool_calls):
                        # fill in the value of local variables
                        messages.append(response)
                        total_tokens = response.usage_metadata["total_tokens"]
                        # logger.info(total_tokens)
                        tool_calls = response.tool_calls
                        tools_config = {
                            "file_mapping": state["file_mapping"],
                            "tech_spec_parsed": state["tech_spec_parsed"],
                            "company_id": self.company_id,
                            "repo_id": self.repo_id,
                            "repo_name": self.repo_name,
                            "branch_id": self.branch_id,
                            "is_new_dest_repo": self.is_new_dest_repo,
                            "user_id": self.user_id,
                            "head_commit_hash": self.head_commit_hash,
                            "graph_builder": self.graph_builder,
                            "github_server": self.github_server,
                            "git_project_repo_id": self.git_project_repo_id,
                        }
                        for tool_call in tool_calls:
                            tool_message = process_tool_call(
                                tool_call=tool_call,
                                tools_list=rfm_node_tools,
                                tools_config=tools_config
                            )
                            messages = process_messages_with_tool_call(
                                tool_message=tool_message,
                                messages=messages,
                                total_tokens_before_tool=total_tokens,
                                llm=llm
                            )
                        # logger.info(f'sending tool response back to llm for file: {file_path}')
                        response: AIMessage = llm.invoke(messages)

                    content = get_response_content(response=response)

                    # logger.info(content)

                    state["current_folder_structure"] = content
                    break
                except AnthropicBadRequestError as e:
                    logger.warning(f'Anthropic bad request error, trying with fallback index {retry_count}: {e}')
                    if retry_count < len(self.fallback_llms):
                        llm = self.fallback_llms[retry_count]
                        retry_count += 1
                    else:
                        logger.error(f'Anthropic bad request error, all fallback llms failed: {e}')
                        raise e
        except (RETRYABLE_EXCEPTIONS) as e:
            logger.warning(f'encountered error, retrying: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise
        except Exception as e:
            logger.warning(f'encountered error, failing job: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise

        # logger.info(f'current folder structure: {state["current_folder_structure"]}')

        logger.info(
            f'finished generating updated repo structure for folder: {"root" if state["current_folder_path"] == "" else state["current_folder_path"]}')

        return get_state(state=state)

    @archie_exponential_retry(
        exceptions=RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS
    )
    def format_updated_folder_structure(self, state: ReverseMapperState) -> Dict[str, Any]:
        folder_path = state["current_folder_path"]
        logger.info(f'formatting updated repo structure for folder: {"root" if folder_path == "" else folder_path}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": STRUCTURE_SYSTEM_PROMPT
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": STRUCTURE_INPUT.format(
                        assigned_output=state["current_folder_structure"]
                    )
                }
            ])
        ]

        structured_llm = self.structure_llm.with_structured_output(RepositoryUpdatedFolderContents)

        try:
            response: AIMessage = structured_llm.invoke(messages)
        except (RETRYABLE_EXCEPTIONS + SUPPLEMENTARY_RETRYABLE_EXCEPTIONS) as e:
            logger.warning(f'encountered error, retrying: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise
        except Exception as e:
            logger.warning(f'encountered error, failing job: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise

        response_dict: RepositoryUpdatedFolderContents = response.model_dump()

        # logger.info(response_dict)

        self.process_updated_response(state=state, response_dict=response_dict)

        # logger.info(f'final repo structure: {state["file_mapping"]}')

        logger.info(
            f'finished formatting updated repo structure for folder: {"root" if state["current_folder_path"] == "" else state["current_folder_path"]}')

        self.upload_state(state=state)

        return get_state(state=state)

    def process_updated_response(self, state: ReverseMapperState, response_dict: RepositoryUpdatedFolderContents):
        # Assumes that model is good enough to ensure that folder will never be in both, created and modified
        for folder in reversed(response_dict["created_folders"]):
            folder_path = clean_path(folder["dest_path"])
            if not self.is_valid_child_path(state=state, path=folder_path):
                logger.warning(f"Skipping folder with invalid path: {folder_path}")
                continue
            if folder_path not in state["visited_folders"] and folder_path not in state["pending_folders"]:
                logger.info(f'adding new pending folder: {folder_path}')
                state["pending_folders"].insert(0, folder_path)
                state["pending_folder_details"][folder_path] = folder
            else:
                logger.warning(f'folder path already visited or pending: {folder_path}')

        for folder in reversed(response_dict["modified_folders"]):
            folder_path = clean_path(folder["dest_path"])
            folder["source_folders"] = [folder_path]
            if not self.is_valid_child_path(state=state, path=folder_path):
                logger.warning(f"Skipping folder with invalid path: {folder_path}")
                continue
            if folder_path not in state["visited_folders"] and folder_path not in state["pending_folders"]:
                logger.info(f'adding updated pending folder: {folder_path}')
                state["pending_folders"].insert(0, folder_path)
                state["pending_folder_details"][folder_path] = folder
            else:
                logger.warning(f'folder path already visited or pending: {folder_path}')

        for folder in reversed(response_dict["unchanged_folders"]):
            folder_path = clean_path(folder["dest_path"])
            if not self.is_valid_child_path(state=state, path=folder_path):
                logger.warning(f"Skipping excluded folder with invalid path: {folder_path}")
                continue
            if folder_path not in state["excluded_folders"]:
                logger.info(f'adding new excluded folder: {folder_path}')
                state["excluded_folders"].append(folder)
            else:
                logger.warning(f'folder path already excluded: {folder_path}')

        for file in reversed(response_dict["unchanged_files"]):
            file_path = clean_path(file["dest_path"])
            if not self.is_valid_child_path(state=state, path=file_path):
                logger.warning(f"Skipping excluded file with invalid path: {file_path}")
                continue
            if file_path not in state["excluded_files"]:
                logger.info(f'adding new excluded file: {file_path}')
                state["excluded_files"].append(file)
            else:
                logger.warning(f'file path already excluded: {file_path}')

        for file in reversed(response_dict["deleted_files"]):
            file_path = clean_path(file["dest_path"])
            if not self.is_valid_child_path(state=state, path=file_path):
                logger.warning(f"Skipping deleted file with invalid path: {file_path}")
                continue
            current_files_list = state["short_repo_structure"].get(state["current_folder_path"], [])
            if file_path not in current_files_list:
                logger.info(f'adding deleted file path to repo structure: {file_path}')
                file["status"] = FileOrFolderStatus.DELETED.value
                self.add_file_to_state(state=state, file=file, file_path=file_path)
            else:
                logger.warning(f'file path already exists in repo structure: {file_path}')

        for file in reversed(response_dict["modified_files"]):
            file_path = clean_path(file["dest_path"])

            if not self.is_valid_child_path(state=state, path=file_path):
                logger.warning(f"Skipping modified file with invalid path: {file_path}")
                continue

            if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
                logger.warning(f"Skipping invalid modified file: {file_path}")
                continue

            current_files_list = state["short_repo_structure"].get(state["current_folder_path"], [])
            if file_path not in current_files_list:
                logger.info(f'adding modified file path to repo structure: {file_path}')
                file["status"] = FileOrFolderStatus.UPDATED.value
                self.add_file_to_state(state=state, file=file, file_path=file_path)
            else:
                logger.warning(f'file path already exists in repo structure: {file_path}')

        for file in reversed(response_dict["created_files"]):
            file_path = clean_path(file["dest_path"])
            if not self.is_valid_child_path(state=state, path=file_path):
                logger.warning(f"Skipping created file with invalid path: {file_path}")
                continue

            if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
                logger.warning(f"Skipping invalid created file: {file_path}")
                continue

            current_files_list = state["short_repo_structure"].get(state["current_folder_path"], [])
            if file_path not in current_files_list:
                logger.info(f'adding created file path to repo structure: {file_path}')
                file["status"] = FileOrFolderStatus.CREATED.value
                self.add_file_to_state(state=state, file=file, file_path=file_path)
            else:
                logger.warning(f'file path already exists in repo structure: {file_path}')

    def add_file_to_state(self, state: ReverseMapperState, file, file_path):
        file_mapping_folder_path = state["file_mapping"].get(state["current_folder_path"], [])
        file_mapping_folder_path.append(file)
        state["file_mapping"][state["current_folder_path"]] = file_mapping_folder_path
        repo_structure_folder_path = state["short_repo_structure"].get(state["current_folder_path"], [])
        repo_structure_folder_path.append(file_path)
        state["short_repo_structure"][state["current_folder_path"]] = repo_structure_folder_path
        state["file_schemas"][file_path] = file
