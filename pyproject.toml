[project]
name = "blitzy_platform_shared"
version = "0.0.480"
description = "Blitzy Platform Shared"
requires-python = ">=3.11"
classifiers = [ "Programming Language :: Python :: 3", "License :: OSI Approved :: MIT License", "Operating System :: OS Independent",]
dependencies = [ "python-json-logger>=3.3.0", "structlog>=25.3.0", "google-api-core>=2.25.0", "google-auth>=2.40.3", "requests>=2.32.3", "PyGithub>=2.6.1", "pydantic>=2.11.5", "langchain-anthropic>=0.3.17", "langchain-aws>=0.2.27", "langchain-core>=0.3.68", "langchain-neo4j>=0.4.0", "langchain-google-vertexai>=2.0.27", "langchain-openai>=0.3.27", "langchain-voyageai>=0.1.6", "langgraph>=0.5.1", "langsmith>=0.4.4", "blitzy-utils==0.0.334", "networkx>=3.5", "tenacity>=8.3.0", "thefuzz>=0.22.1", "transformers>=4.52.4",]
[[project.authors]]
name = "Sid Pardeshi"
email = "<EMAIL>"

