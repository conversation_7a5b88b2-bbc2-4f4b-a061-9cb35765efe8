from typing import Dict, Any, List
from enum import Enum
import logging
import socket

from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, SessionExpired, DriverError
from pydantic import ValidationError
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
    after_log
)

from ..common.consts import DEFAULT_MAX_RETRIES, DEFAULT_MIN_WAIT, DEFAULT_MAX_WAIT, DEFAULT_MULTIPLIER
from .schema.globals import CodeGraphGlobalsSchema
from .schema.imports import CodeGraphImportsSchema, MemberAccess
from .schema.functions import CodeGraphFunctionSchema, References
from .schema.classes import CodeGraphClassSchema
from .schema.summary import FileSummary
from .schema.exports import CodeGraphExportsSchema
from .schema.src_adjacent import Section
from .consts import BatchStatus
from .utils import get_parent_folder_path
from blitzy_utils.logger import logger


class Visibility(str, Enum):
    SHARED = "SHARED"
    PRIVATE = "PRIVATE"


class GraphError(Exception):
    """Base exception for graph-related errors"""
    pass


class CodeGraphBuilder:
    def __init__(
        self,
        uri: str,
        username: str,
        password: str,
        company_id: str,
        repo_id: str,
        branch_id: str,
        head_commit_hash: str
    ):
        self.uri = uri
        self.username = username
        self.password = password
        self.company_id = company_id
        self.repo_id = repo_id
        self.branch_id = branch_id
        self.head_commit_hash = head_commit_hash

        self.driver = None
        self.connect()

    @retry(
        stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
        wait=wait_exponential(multiplier=DEFAULT_MULTIPLIER, min=DEFAULT_MIN_WAIT, max=DEFAULT_MAX_WAIT),
        retry=retry_if_exception_type((
            ServiceUnavailable,
            SessionExpired,
            ConnectionResetError,
            DriverError,
            socket.error
        )),
        before_sleep=before_sleep_log(logger.logger, logging.WARNING),
        after=after_log(logger.logger, logging.INFO),
        retry_error_callback=lambda retry_state: retry_state.outcome.result()
    )
    def connect(self):
        """Establish connection to Neo4j with retry logic"""
        if self.driver is not None:
            try:
                self.driver.close()
            except Exception as e:
                logger.warning(f"Error closing existing driver: {e}")

        self.driver = GraphDatabase.driver(
            uri=self.uri,
            auth=(self.username, self.password),
            max_connection_lifetime=3600  # Refresh connections hourly
        )
        self.driver.verify_connectivity()
        logger.info('Graph DB connection established')

    def close(self):
        """Safely close the database connection"""
        if self.driver is not None:
            self.driver.close()
            self.driver = None

    def ensure_connection(self):
        """Ensure we have a valid connection, reconnecting if necessary"""
        try:
            if self.driver is None:
                self.connect()
            else:
                self.driver.verify_connectivity()
        except Exception as e:
            logger.warning(f"Connection verification failed: {e}")
            self.connect()

    def setup_constraints(self):
        """Create uniqueness constraints for the graph"""
        constraints = [
            # Organization/structure constraints
            "CREATE CONSTRAINT company_id_unique IF NOT EXISTS FOR (c:COMPANY) REQUIRE c.company_id IS UNIQUE",
            "CREATE CONSTRAINT repo_id_unique IF NOT EXISTS FOR (r:REPO) REQUIRE (r.company_id, r.repo_id) IS UNIQUE",
            "CREATE CONSTRAINT branch_id_unique IF NOT EXISTS FOR (b:BRANCH) REQUIRE (b.company_id, b.repo_id, b.branch_id) IS UNIQUE",

            # File system constraint
            "CREATE CONSTRAINT folder_branch_path_unique IF NOT EXISTS FOR (f:FOLDER) REQUIRE (f.company_id, f.repo_id, f.branch_id, f.path) IS UNIQUE",
            "CREATE CONSTRAINT file_branch_path_unique IF NOT EXISTS FOR (f:FILE) REQUIRE (f.company_id, f.repo_id, f.branch_id, f.path) IS UNIQUE",
            "CREATE CONSTRAINT file_path_not_null IF NOT EXISTS FOR (f:FILE) REQUIRE f.path IS NOT NULL",

            # Globals constraints
            "CREATE CONSTRAINT declaration_branch_file_name_unique IF NOT EXISTS FOR (d:DECLARATION) REQUIRE (d.company_id, d.repo_id, d.branch_id, d.file_path, d.name) IS UNIQUE",
            "CREATE CONSTRAINT type_definition_branch_file_name_unique IF NOT EXISTS FOR (t:TYPE_DEFINITION) REQUIRE (t.company_id, t.repo_id, t.branch_id, t.file_path, t.name) IS UNIQUE",

            # Import and member constraints
            "CREATE CONSTRAINT internal_import_branch_file_name_unique IF NOT EXISTS FOR (i:INTERNAL_IMPORT) REQUIRE (i.company_id, i.repo_id, i.branch_id, i.file_path, i.name) IS UNIQUE",
            "CREATE CONSTRAINT external_import_branch_file_name_unique IF NOT EXISTS FOR (e:EXTERNAL_IMPORT) REQUIRE (e.company_id, e.repo_id, e.branch_id, e.file_path, e.name) IS UNIQUE",
            "CREATE CONSTRAINT member_branch_file_name_unique IF NOT EXISTS FOR (m:MEMBER) REQUIRE (m.company_id, m.repo_id, m.branch_id, m.file_path, m.name) IS UNIQUE",
            "CREATE CONSTRAINT sub_member_member_name_unique IF NOT EXISTS FOR (s:SUB_MEMBER) REQUIRE (s.company_id, s.repo_id, s.branch_id, s.file_path, s.member_name, s.name) IS UNIQUE",

            # Function-related constraints
            "CREATE CONSTRAINT function_branch_file_name_unique IF NOT EXISTS FOR (f:FUNCTION) REQUIRE (f.company_id, f.repo_id, f.branch_id, f.file_path, f.name) IS UNIQUE",
            "CREATE CONSTRAINT generic_param_branch_function_name_unique IF NOT EXISTS FOR (g:GENERIC_PARAM) REQUIRE (g.company_id, g.repo_id, g.branch_id, g.file_path, g.function_name, g.name) IS UNIQUE",
            "CREATE CONSTRAINT signature_branch_function_unique IF NOT EXISTS FOR (s:SIGNATURE) REQUIRE (s.company_id, s.repo_id, s.branch_id, s.file_path, s.function_name) IS UNIQUE",
            "CREATE CONSTRAINT function_parameter_branch_unique IF NOT EXISTS FOR (p:FUNCTION_PARAMETER) REQUIRE (p.company_id, p.repo_id, p.branch_id, p.file_path, p.function_name, p.name) IS UNIQUE",
            "CREATE CONSTRAINT decorator_branch_function_name_unique IF NOT EXISTS FOR (d:FUNCTION_DECORATOR) REQUIRE (d.company_id, d.repo_id, d.branch_id, d.file_path, d.function_name, d.name) IS UNIQUE",
            "CREATE CONSTRAINT step_branch_function_id_unique IF NOT EXISTS FOR (s:STEP) REQUIRE (s.company_id, s.repo_id, s.branch_id, s.file_path, s.function_name, s.step_id) IS UNIQUE",

            # Class-related constraints
            "CREATE CONSTRAINT class_branch_file_name_unique IF NOT EXISTS FOR (c:CLASS) REQUIRE (c.company_id, c.repo_id, c.branch_id, c.file_path, c.name) IS UNIQUE",
            "CREATE CONSTRAINT class_property_branch_unique IF NOT EXISTS FOR (p:CLASS_PROPERTY) REQUIRE (p.company_id, p.repo_id, p.branch_id, p.file_path, p.class_name, p.name) IS UNIQUE",
            "CREATE CONSTRAINT method_branch_unique IF NOT EXISTS FOR (m:METHOD) REQUIRE (m.company_id, m.repo_id, m.branch_id, m.file_path, m.class_name, m.name) IS UNIQUE",
            "CREATE CONSTRAINT method_signature_branch_unique IF NOT EXISTS FOR (s:METHOD_SIGNATURE) REQUIRE (s.company_id, s.repo_id, s.branch_id, s.file_path, s.class_name, s.method_name) IS UNIQUE",
            "CREATE CONSTRAINT method_parameter_branch_unique IF NOT EXISTS FOR (p:METHOD_PARAMETER) REQUIRE (p.company_id, p.repo_id, p.branch_id, p.file_path, p.class_name, p.method_name, p.name) IS UNIQUE",
            "CREATE CONSTRAINT constructor_branch_unique IF NOT EXISTS FOR (c:CONSTRUCTOR) REQUIRE (c.company_id, c.repo_id, c.branch_id, c.file_path, c.class_name) IS UNIQUE",
            "CREATE CONSTRAINT constructor_parameter_branch_unique IF NOT EXISTS FOR (p:CONSTRUCTOR_PARAMETER) REQUIRE (p.company_id, p.repo_id, p.branch_id, p.file_path, p.class_name, p.name) IS UNIQUE",
            "CREATE CONSTRAINT class_generic_param_branch_unique IF NOT EXISTS FOR (g:GENERIC_PARAM) REQUIRE (g.company_id, g.repo_id, g.branch_id, g.file_path, g.class_name, g.name) IS UNIQUE",
            "CREATE CONSTRAINT class_decorator_branch_unique IF NOT EXISTS FOR (d:CLASS_DECORATOR) REQUIRE (d.company_id, d.repo_id, d.branch_id, d.file_path, d.class_name, d.name) IS UNIQUE",
            "CREATE CONSTRAINT method_decorator_branch_unique IF NOT EXISTS FOR (d:METHOD_DECORATOR) REQUIRE (d.company_id, d.repo_id, d.branch_id, d.file_path, d.class_name, d.method_name, d.name) IS UNIQUE",
            "CREATE CONSTRAINT method_step_branch_unique IF NOT EXISTS FOR (s:STEP) REQUIRE (s.company_id, s.repo_id, s.branch_id, s.file_path, s.class_name, s.method_name, s.step_id) IS UNIQUE",

            # Export-related constraints
            "CREATE CONSTRAINT export_branch_file_name_unique IF NOT EXISTS FOR (ex:EXPORT) REQUIRE (ex.company_id, ex.repo_id, ex.branch_id, ex.file_path, ex.name) IS UNIQUE",

            # Section constraint
            "CREATE CONSTRAINT section_branch_file_name_unique IF NOT EXISTS FOR (s:SECTION) REQUIRE (s.company_id, s.repo_id, s.branch_id, s.file_path, s.name) IS UNIQUE",

            # Reference constraint
            "CREATE CONSTRAINT reference_branch_file_name_unique IF NOT EXISTS FOR (r:REFERENCE) REQUIRE (r.company_id, r.repo_id, r.branch_id, r.file_path, r.name) IS UNIQUE"
        ]

        for constraint in constraints:
            self.execute_query(constraint)

    @retry(
        stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
        wait=wait_exponential(multiplier=DEFAULT_MULTIPLIER, min=DEFAULT_MIN_WAIT, max=DEFAULT_MAX_WAIT),
        retry=retry_if_exception_type((
            ServiceUnavailable,
            SessionExpired,
            ConnectionResetError,
            DriverError,
            socket.error
        )),
        before_sleep=before_sleep_log(logger.logger, logging.WARNING),
        after=after_log(logger.logger, logging.INFO),
        retry_error_callback=lambda retry_state: retry_state.outcome.result()
    )
    def execute_query(self, query: str, return_single_result=False, **params):
        """Execute a Neo4j query with retry logic"""
        self.ensure_connection()

        try:
            with self.driver.session() as session:
                result = session.run(query, **params)
                if return_single_result:
                    return result.single()
                return result.data()
        except Exception as e:
            logger.error(f"Query execution failed: {e}\nQuery: {query}\nParams: {params}")
            raise

    def setup_branch(
        self,
        company_id: str,
        repo_id: str,
        repo_name: str,
        branch_id: str,
        head_commit_hash: str
    ):
        """Create the complete graph structure for a file

        Raises:
            GraphError: If visibility is SHARED but no team_id is provided
        """
        self.setup_constraints()

        if company_id == '':
            company_id = None

        if company_id == None:
            raise GraphError("Company id cannot be None")

        query = """
            // Merge COMPANY (optional)
            MERGE (c:COMPANY {company_id: $company_id})
            ON CREATE SET c.created_at = timestamp()
            
            // Create Repository
            WITH c
            MERGE (r:REPO {company_id: $company_id, repo_id: $repo_id})
            ON CREATE SET r.name = $repo_name, 
                        r.created_at = timestamp()
            
            // Link Repository to Company
            MERGE (c)-[:HAS_REPO]->(r)
            
            // Create Branch
            WITH r
            MERGE (b:BRANCH {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            ON CREATE SET b.created_at = timestamp(),
                b.head_commit_hash = $head_commit_hash
            ON MATCH SET b.head_commit_hash = $head_commit_hash
            MERGE (r)-[:HAS_BRANCH]->(b)
            
            // Create Root Folder under Branch
            WITH b
            MERGE (root:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: ""})
            ON CREATE SET root.name = "Root"
            MERGE (b)-[:HAS_FOLDER]->(root)
            
            // Return nodes for folder creation
            RETURN b, root
        """

        record = self.execute_query(
            query,
            return_single_result=True,
            company_id=company_id,
            repo_id=repo_id,
            repo_name=repo_name,
            branch_id=branch_id,
            head_commit_hash=head_commit_hash
        )
        if not record:
            raise GraphError("Failed to create base structure")

        return record

    def setup_file(
        self,
        company_id: str,
        repo_id: str,
        branch_id: str,
        head_commit_hash: str,
        file_path: str
    ):
        """Create the complete graph structure for a file along with folder structure and file node for a given path

         Raises:
            GraphError: If visibility is SHARED but no team_id is provided
        """

        folders = file_path.split('/')
        file_name = folders[-1]  # Last element is the file name
        folder_path = folders[:-1]  # All but last element are folders

        current_path = ""
        parent_path = ""

        # Create folder chain
        for folder in folder_path:
            parent_path = current_path
            current_path = f"{current_path}/{folder}".lstrip('/')

            query = """
                MATCH (parent:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $parent_path})
                MERGE (child:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $current_path})
                ON CREATE SET 
                    child.name = $folder_name,
                    child.head_commit_hash = $head_commit_hash,
                    child.created_at = timestamp()
                ON MATCH SET 
                    child.head_commit_hash = $head_commit_hash
                MERGE (parent)-[:HAS_FOLDER]->(child)
            """

            self.execute_query(
                query,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                parent_path=parent_path,
                current_path=current_path,
                folder_name=folder,
                head_commit_hash=head_commit_hash
            )

        # Create file node
        file_query = """
            MATCH (folder:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $folder_path})
            MERGE (file:FILE {
                company_id: $company_id,
                repo_id: $repo_id,
                branch_id: $branch_id,
                path: $file_path
            })
            ON CREATE SET 
                file.created_at = timestamp(),
                file.name = $file_name,
                file.head_commit_hash = $head_commit_hash
            ON MATCH SET 
                file.head_commit_hash = $head_commit_hash
            MERGE (folder)-[:HAS_FILE]->(file)
        """

        self.execute_query(
            file_query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            head_commit_hash=head_commit_hash,
            folder_path=current_path,
            file_path=file_path,
            file_name=file_name
        )

    def graph_globals(self, file_path: str, company_id: str, repo_id: str, branch_id: str, globals: Dict[str, Any]):
        """Create nodes and relationships for file-level declarations and type definitions

        Args:
            file_path: The full path of the file these globals belong to
            globals: Schema-validated globals data containing declarations and type definitions
        """

        globals_data = CodeGraphGlobalsSchema.model_validate(globals)

        # Process type definitions
        type_def_query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            
            MERGE (td:TYPE_DEFINITION {
                company_id: $company_id,
                repo_id: $repo_id,
                branch_id: $branch_id,
                file_path: $file_path,
                name: $name
            })
            ON CREATE SET 
                td.kind = $kind,
                td.definition = $definition,
                td.exported = $exported,
                td.visibility = $visibility,
                td.summary = $summary,
                td.extends = $extends,
                td.created_at = timestamp()
            
            WITH td
            MERGE (f)-[:HAS_TYPE_DEFINITION]->(td)
        """

        # Process declarations
        declaration_query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            
            MERGE (d:DECLARATION {
                company_id: $company_id,
                repo_id: $repo_id,
                branch_id: $branch_id,
                file_path: $file_path,
                name: $name
            })
            ON CREATE SET 
                d.type = $type,
                d.source = $source,
                d.exported = $exported,
                d.mutability = $mutability,
                d.scope = $scope,
                d.initial_value = $initial_value,
                d.lifecycle = $lifecycle,
                d.summary = $summary,
                d.created_at = timestamp()
            
            MERGE (f)-[:HAS_DECLARATION]->(d)
        """

        for type_def in globals_data.type_definitions:
            self.execute_query(
                type_def_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                name=type_def.name,
                kind=type_def.kind.value,
                definition=type_def.definition,
                exported=type_def.exported,
                visibility=type_def.visibility.value,
                summary=type_def.summary,
                extends=type_def.extends
            )

        # Create declaration nodes
        for decl in globals_data.declarations:
            self.execute_query(
                declaration_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                name=decl.name,
                type=decl.type,
                source=decl.source.value,
                exported=decl.exported,
                mutability=decl.mutability.value,
                scope=decl.scope.value,
                initial_value=decl.initial_value,
                lifecycle=decl.lifecycle.value,
                summary=decl.summary
            )

    def graph_imports(self, file_path: str, company_id: str, repo_id: str, branch_id: str, imports: Dict[str, Any]):
        """Create nodes and relationships for file imports and their members

        Args:
            file_path: The full path of the file these imports belong to
            imports: Schema-validated imports data containing internal and external imports
        """
        imports_data = CodeGraphImportsSchema.model_validate(imports)

        # Process internal imports
        internal_import_query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            
            MERGE (i:INTERNAL_IMPORT {
                company_id: $company_id, 
                repo_id: $repo_id,
                branch_id: $branch_id,
                file_path: $file_path,
                name: $name
            })
            ON CREATE SET 
                // Import mechanism properties
                i.import_style = $import_mechanism.style,
                i.module_system = $import_mechanism.module_system,
                i.loading_strategy = $import_mechanism.loading_strategy,
                i.visibility_scope = $import_mechanism.visibility_scope,
                i.alias = $import_mechanism.alias,
                i.is_type_only = $import_mechanism.is_type_only,
                i.is_default = $import_mechanism.is_default,
                i.is_namespace = $import_mechanism.is_namespace,
                i.is_reexported = $import_mechanism.is_reexported,
                
                // Kind properties
                i.import_category = $kind.category,
                i.usage_type = $kind.usage,
                
                // Source properties
                i.module_path = $source.module_path,
                i.full_path = $source.full_path,
                i.is_relative = $source.is_relative,
                i.is_side_effect = $source.is_side_effect,
                i.root_relative_path = $source.root_relative_path,
                i.is_conditional = $source.is_conditional,
                
                i.purpose = $purpose,
                i.summary = $summary,
                i.created_at = timestamp()
            
            MERGE (f)-[:HAS_INTERNAL_IMPORT]->(i)
            
            // Create member nodes and relationships
            WITH i
            UNWIND $members_accessed as member
            MERGE (m:MEMBER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: member.name})
            ON CREATE SET
                m += CASE WHEN member.category IS NOT NULL THEN {category: member.category} ELSE {} END,
                m += CASE WHEN member.usage IS NOT NULL THEN {usage: member.usage} ELSE {} END,
                m += CASE WHEN member.access_type IS NOT NULL THEN {access_type: member.access_type} ELSE {} END,
                m += CASE WHEN member.optional IS NOT NULL THEN {optional: member.optional} ELSE {} END

            MERGE (i)-[:ACCESSES_MEMBER]->(m)
            
            // Create sub-member nodes and relationships if they exist
            WITH m, member
            WHERE member.sub_members IS NOT NULL AND size(member.sub_members) > 0
            UNWIND member.sub_members as sub_member
            MERGE (sm:SUB_MEMBER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, member_name: m.name, name: sub_member})
            ON CREATE SET sm.created_at = timestamp()
            MERGE (m)-[:HAS_SUB_MEMBER]->(sm)
        """

        # Process external imports
        external_import_query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            
            MERGE (e:EXTERNAL_IMPORT {
                company_id: $company_id, 
                repo_id: $repo_id,
                branch_id: $branch_id,
                file_path: $file_path,
                name: $name
            })
            ON CREATE SET 
                // Import mechanism properties
                e.import_style = $import_mechanism.style,
                e.module_system = $import_mechanism.module_system,
                e.loading_strategy = $import_mechanism.loading_strategy,
                e.visibility_scope = $import_mechanism.visibility_scope,
                e.alias = $import_mechanism.alias,
                e.is_type_only = $import_mechanism.is_type_only,
                e.is_default = $import_mechanism.is_default,
                e.is_namespace = $import_mechanism.is_namespace,
                e.is_reexported = $import_mechanism.is_reexported,
                
                // Kind properties
                e.import_category = $kind.category,
                e.usage_type = $kind.usage,
                
                // Package properties
                e.package_name = $package.name,
                e.package_version = $package.version,
                e.package_category = $package.category,
                e.package_registry = $package.registry,
                e.is_dev_dependency = $package.is_dev_dependency,
                
                e.purpose = $purpose,
                e.min_version_required = $min_version_required,
                e.summary = $summary,
                e.created_at = timestamp()
            
            MERGE (f)-[:HAS_EXTERNAL_IMPORT]->(e)
            
            // Create member nodes and relationships
            WITH e
            UNWIND $members_accessed as member
            MERGE (m:MEMBER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: member.name})
            ON CREATE SET
                m += CASE WHEN member.category IS NOT NULL THEN {category: member.category} ELSE {} END,
                m += CASE WHEN member.usage IS NOT NULL THEN {usage: member.usage} ELSE {} END,
                m += CASE WHEN member.access_type IS NOT NULL THEN {access_type: member.access_type} ELSE {} END,
                m += CASE WHEN member.optional IS NOT NULL THEN {optional: member.optional} ELSE {} END
            MERGE (e)-[:ACCESSES_MEMBER]->(m)
            
            // Create sub-member nodes and relationships if they exist
            WITH m, member
            WHERE member.sub_members IS NOT NULL AND size(member.sub_members) > 0
            UNWIND member.sub_members as sub_member
            MERGE (sm:SUB_MEMBER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, member_name: m.name, name: sub_member})
            ON CREATE SET sm.created_at = timestamp()
            MERGE (m)-[:HAS_SUB_MEMBER]->(sm)
        """

        for internal_import in imports_data.internal:
            self.execute_query(
                internal_import_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                name=internal_import.name,
                import_mechanism={
                    'style': internal_import.import_mechanism.style,
                    'module_system': internal_import.import_mechanism.module_system,
                    'loading_strategy': internal_import.import_mechanism.loading_strategy,
                    'visibility_scope': internal_import.import_mechanism.visibility_scope,
                    'alias': internal_import.import_mechanism.alias,
                    'is_type_only': internal_import.import_mechanism.is_type_only,
                    'is_default': internal_import.import_mechanism.is_default,
                    'is_namespace': internal_import.import_mechanism.is_namespace,
                    'is_reexported': internal_import.import_mechanism.is_reexported
                },
                kind={
                    'category': internal_import.kind.category,
                    'usage': internal_import.kind.usage
                },
                source={
                    'module_path': internal_import.source.module_path,
                    'full_path': internal_import.source.full_path,
                    'is_relative': internal_import.source.is_relative,
                    'is_side_effect': internal_import.source.is_side_effect,
                    'root_relative_path': internal_import.source.root_relative_path,
                    'is_conditional': internal_import.source.is_conditional
                },
                purpose=internal_import.purpose,
                summary=internal_import.summary,
                members_accessed=[self.prepare_member_data(m) for m in internal_import.members_accessed]
            )

        # Create external import nodes and relationships
        for external_import in imports_data.external:
            self.execute_query(
                external_import_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                name=external_import.name,
                import_mechanism={
                    'style': external_import.import_mechanism.style,
                    'module_system': external_import.import_mechanism.module_system,
                    'loading_strategy': external_import.import_mechanism.loading_strategy,
                    'visibility_scope': external_import.import_mechanism.visibility_scope,
                    'alias': external_import.import_mechanism.alias,
                    'is_type_only': external_import.import_mechanism.is_type_only,
                    'is_default': external_import.import_mechanism.is_default,
                    'is_namespace': external_import.import_mechanism.is_namespace,
                    'is_reexported': external_import.import_mechanism.is_reexported
                },
                kind={
                    'category': external_import.kind.category,
                    'usage': external_import.kind.usage
                },
                package={
                    'name': external_import.package.name,
                    'version': external_import.package.version,
                    'category': external_import.package.category,
                    'registry': external_import.package.registry,
                    'is_dev_dependency': external_import.package.is_dev_dependency
                },
                purpose=external_import.purpose,
                min_version_required=external_import.min_version_required,
                summary=external_import.summary,
                members_accessed=[self.prepare_member_data(m) for m in external_import.members_accessed]
            )

    def prepare_member_data(self, member: MemberAccess):
        """Flatten member data for Neo4j storage"""
        return {
            'name': member.name,
            'category': member.kind.category,
            'usage': member.kind.usage,
            'access_type': member.access_type,
            'optional': member.optional,
            'sub_members': member.sub_members if member.sub_members is not None else []
        }

    def graph_functions(self, file_path: str, company_id: str, repo_id: str, branch_id: str, functions_list: List[Dict[str, Any]]):
        """Create nodes and relationships for functions in a file

        Args:
            file_path: The full path of the file these functions belong to
            functions: Schema-validated function data containing function definitions
        """
        # Process each function
        for function in functions_list:
            function_data = CodeGraphFunctionSchema.model_validate(function)

            # Create flattened summary string
            flattened_summary = f"""PURPOSE
            {function_data.summary.purpose}

            INPUTS
            {'\n'.join(f"- {input_desc}" for input_desc in (function_data.summary.inputs or []))}

            OUTPUTS
            {'\n'.join(f"- {output_desc}" for output_desc in (function_data.summary.outputs or []))}

            BEHAVIORS
            {'\n'.join(f"- {behavior}" for behavior in (function_data.summary.behaviors or []))}

            SIDE EFFECTS
            {'\n'.join(f"- {effect}" for effect in (function_data.summary.side_effects or []))}

            USAGE CONTEXT
            {function_data.summary.usage_context}
            """

            function_query = """
                MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
                
                MERGE (fn:FUNCTION {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $branch_id,
                    file_path: $file_path,
                    name: $name
                })
                ON CREATE SET 
                    // Basic fields
                    fn.kind = $kind,
                    fn.is_async = $is_async,
                    fn.exported = $exported,
                    fn.visibility = $visibility,
                    
                    // Flattened configuration
                    fn.cancellable = $config.cancellable,
                    fn.timeout = $config.timeout,
                    fn.retry_policy_max_attempts = $config.retry_policy_max_attempts,
                    fn.backoff_time_seconds = $config.backoff_time_seconds,
                    fn.backoff_is_exponential = $config.backoff_is_exponential,
                    fn.backoff_max_time_seconds = $config.backoff_max_time_seconds,
                    
                    // Flattened summary
                    fn.purpose = $summary.purpose,
                    fn.inputs = $summary.inputs,
                    fn.outputs = $summary.outputs,
                    fn.behaviors = $summary.behaviors,
                    fn.side_effects = $summary.side_effects,
                    fn.usage_context = $summary.usage_context,
                    fn.summary = $summary_text,
                    
                    fn.created_at = timestamp()
                
                MERGE (f)-[:HAS_FUNCTION]->(fn)
                
                RETURN fn
            """

            # Prepare configuration data
            config = {
                'cancellable': function_data.configuration.cancellable if function_data.configuration else None,
                'timeout': function_data.configuration.timeout if function_data.configuration else None,
                'retry_policy_max_attempts': (
                    function_data.configuration.retry_policy.max_attempts
                    if function_data.configuration and function_data.configuration.retry_policy
                    else None
                ),
                'backoff_time_seconds': (
                    function_data.configuration.retry_policy.backoff.time_seconds
                    if function_data.configuration and function_data.configuration.retry_policy
                    else None
                ),
                'backoff_is_exponential': (
                    function_data.configuration.retry_policy.backoff.is_exponential
                    if function_data.configuration and function_data.configuration.retry_policy
                    else None
                ),
                'backoff_max_time_seconds': (
                    function_data.configuration.retry_policy.backoff.max_time_seconds
                    if function_data.configuration and function_data.configuration.retry_policy
                    else None
                )
            }

            # Execute function creation
            self.execute_query(
                function_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                name=function_data.name,
                kind=function_data.kind.value,
                is_async=function_data.is_async,
                exported=function_data.exported,
                visibility=function_data.visibility.value,
                config=config,
                summary={
                    'purpose': function_data.summary.purpose,
                    'inputs': function_data.summary.inputs,
                    'outputs': function_data.summary.outputs,
                    'behaviors': function_data.summary.behaviors,
                    'side_effects': function_data.summary.side_effects,
                    'usage_context': function_data.summary.usage_context
                },
                summary_text=flattened_summary
            )

            # 2. Generic Parameters
            if function_data.generic_params:
                generic_param_query = """
                    MATCH (fn:FUNCTION {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $function_name})
                    
                    MERGE (gp:GENERIC_PARAM {
                        company_id: $company_id, 
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        function_name: $function_name,
                        name: $param_name
                    })
                    ON CREATE SET 
                        gp.constraints = $constraints,
                        gp.default_type = $default_type,
                        gp.created_at = timestamp()
                    
                    MERGE (fn)-[:HAS_GENERIC_PARAM]->(gp)
                """

                for param in function_data.generic_params:
                    self.execute_query(
                        generic_param_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        function_name=function_data.name,
                        param_name=param.name,
                        constraints=param.constraints,
                        default_type=param.default_type
                    )

            # 3. Signature and Parameters
            signature_query = """
                MATCH (fn:FUNCTION {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $function_name})
                
                MERGE (sig:SIGNATURE {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $branch_id,
                    file_path: $file_path,
                    function_name: $function_name
                })
                ON CREATE SET 
                    sig.return_type = $return_type,
                    sig.throws = $throws,
                    sig.created_at = timestamp()
                
                MERGE (fn)-[:HAS_SIGNATURE]->(sig)
                
                RETURN sig
            """

            self.execute_query(
                signature_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                function_name=function_data.name,
                return_type=function_data.signature.return_type,
                throws=function_data.signature.throws
            )

            # Create parameter nodes
            if function_data.signature.parameters:
                param_query = """
                    MATCH (sig:SIGNATURE {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        function_name: $function_name
                    })
                    
                    MERGE (param:FUNCTION_PARAMETER {
                        company_id: $company_id, 
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        function_name: $function_name,
                        name: $param_name
                    })
                    ON CREATE SET 
                        param.type = $type,
                        param.optional = $optional,
                        param.default_value = $default_value,
                        param.rest_parameter = $rest_parameter,
                        param.description = $description,
                        param.created_at = timestamp()
                    
                    MERGE (sig)-[:HAS_PARAMETER]->(param)
                """

                for param in function_data.signature.parameters:
                    self.execute_query(
                        param_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        function_name=function_data.name,
                        param_name=param.name,
                        type=param.type,
                        optional=param.optional,
                        default_value=param.default_value,
                        rest_parameter=param.rest_parameter,
                        description=param.description
                    )

            # 4. Decorators
            if function_data.decorators:
                decorator_query = """
                    MATCH (fn:FUNCTION {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $function_name})
                    
                    MERGE (dec:FUNCTION_DECORATOR {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        function_name: $function_name,
                        name: $dec_name
                    })
                    ON CREATE SET 
                        dec.arguments = $arguments,
                        dec.purpose = $purpose,
                        dec.created_at = timestamp()
                    
                    MERGE (fn)-[:HAS_FUNCTION_DECORATOR]->(dec)
                """

                for decorator in function_data.decorators:
                    self.execute_query(
                        decorator_query,
                        file_path=file_path,
                        branch_id=branch_id,
                        company_id=company_id,
                        repo_id=repo_id,
                        function_name=function_data.name,
                        dec_name=decorator.name,
                        arguments=decorator.arguments,
                        purpose=decorator.purpose
                    )

            # 5. Steps and Dependencies
            if function_data.steps:
                # First create all step nodes
                step_query = """
                    MATCH (fn:FUNCTION {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $function_name})
                    
                    MERGE (step:STEP {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        function_name: $function_name,
                        step_id: $step_id
                    })
                    ON CREATE SET 
                        step.type = $type,
                        step.description = $description,
                        step.is_async = $is_async,
                        step.critical_section = $critical_section,
                        step.resources = $resources,
                        step.created_at = timestamp()
                    
                    MERGE (fn)-[:HAS_STEP]->(step)
                    
                    RETURN step
                """

                # Store step nodes for dependency linking
                step_nodes = {}
                for step in function_data.steps:
                    record = self.execute_query(
                        step_query,
                        return_single_result=True,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        function_name=function_data.name,
                        step_id=step.id,
                        type=step.type.value,
                        description=step.description,
                        is_async=step.is_async,
                        critical_section=step.critical_section,
                        resources=[r.value for r in step.resources]
                    )
                    step_nodes[step.id] = record['step']

                # Create dependencies between steps
                depends_on_query = """
                    MATCH (s1:STEP {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, function_name: $function_name, step_id: $step1_id})
                    MATCH (s2:STEP {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, function_name: $function_name, step_id: $step2_id})
                    MERGE (s1)-[:DEPENDS_ON]->(s2)
                """

                for step in function_data.steps:
                    if step.depends_on:
                        for dep_id in step.depends_on:
                            # Only create relationship if both steps exist
                            if dep_id in step_nodes:
                                self.execute_query(
                                    depends_on_query,
                                    file_path=file_path,
                                    company_id=company_id,
                                    repo_id=repo_id,
                                    branch_id=branch_id,
                                    function_name=function_data.name,
                                    step1_id=step.id,
                                    step2_id=dep_id
                                )

                # 6. Step References
                if function_data.steps and any(step.references for step in function_data.steps):
                    reference_query = """
                        MATCH (step:STEP {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, function_name: $function_name, step_id: $step_id})
                        MATCH (target) 
                        WHERE (
                            (target:INTERNAL_IMPORT OR target:EXTERNAL_IMPORT) AND target.name = $ref_name
                            OR
                            (target:DECLARATION OR target:TYPE_DEFINITION) AND target.name = $ref_name
                        )
                        MERGE (step)-[:REFERENCES]->(target)
                    """

                    for step in function_data.steps:
                        if step.references:
                            # Process imports
                            imports = step.references.imports or []
                            for imp in imports:
                                self.execute_query(
                                    reference_query,
                                    file_path=file_path,
                                    company_id=company_id,
                                    repo_id=repo_id,
                                    branch_id=branch_id,
                                    function_name=function_data.name,
                                    step_id=step.id,
                                    ref_name=imp.source_name
                                )

                            # Process globals
                            globals = step.references.globals or []
                            for glob in globals:
                                self.execute_query(
                                    reference_query,
                                    file_path=file_path,
                                    company_id=company_id,
                                    repo_id=repo_id,
                                    branch_id=branch_id,
                                    function_name=function_data.name,
                                    step_id=step.id,
                                    ref_name=glob.name
                                )

                            # Process classes
                            classes = step.references.classes
                            for cls in classes:
                                self.execute_query(
                                    reference_query,
                                    file_path=file_path,
                                    company_id=company_id,
                                    repo_id=repo_id,
                                    branch_id=branch_id,
                                    function_name=function_data.name,
                                    step_id=step.id,
                                    ref_name=cls.name
                                )

    def graph_classes(self, file_path: str, company_id: str, repo_id: str, branch_id: str, classes_list: List[Dict[str, Any]]):
        """
        Create nodes and relationships for classes in a file.

        Args:
            file_path: The full path of the file these classes belong to.
            classes_list: A list of dictionaries, each conforming to CodeGraphClassSchema,
                        describing class definitions to be recorded in the graph.
        """
        for raw_class in classes_list:
            # 1. Validate/parse the incoming data with the Pydantic model
            class_data = CodeGraphClassSchema.model_validate(raw_class)

            # Safely handle summary lists with defaults
            key_behaviors = class_data.summary.key_behaviors or []
            dependencies = class_data.summary.dependencies or []

            # Create flattened summary string
            flattened_summary = f"""PURPOSE
            {class_data.summary.purpose}

            KEY BEHAVIORS
            {'\n'.join(f"- {behavior}" for behavior in key_behaviors)}

            DEPENDENCIES
            {'\n'.join(f"- {dep}" for dep in dependencies)}

            USAGE CONTEXT
            {class_data.summary.usage_context}

            STATE MANAGEMENT
            {class_data.summary.state_management}
            """

            # 2. Create/MERGE the Class node and flatten its basic fields
            class_query = """
                MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
                
                MERGE (cls:CLASS {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $branch_id,
                    file_path: $file_path,
                    name: $class_name
                })
                ON CREATE SET
                    cls.kind = $kind,
                    cls.exported = $exported,
                    
                    // Flatten inheritance arrays directly on the class node
                    cls.extends = $extends,
                    cls.implements = $implements,
                    cls.mixins = $mixins,

                    // Flatten summary fields
                    cls.summary_purpose = $summary_purpose,
                    cls.summary_key_behaviors = $summary_key_behaviors,
                    cls.summary_dependencies = $summary_dependencies,
                    cls.summary_usage_context = $summary_usage_context,
                    cls.summary_state_management = $summary_state_management,
                    cls.summary = $summary,

                    cls.created_at = timestamp()
                
                MERGE (f)-[:HAS_CLASS]->(cls)
                
                RETURN cls
            """

            self.execute_query(
                class_query,
                file_path=file_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                class_name=class_data.name,
                kind=class_data.kind.value,
                exported=class_data.exported,
                extends=class_data.inheritance.extends,
                implements=class_data.inheritance.implements,
                mixins=class_data.inheritance.mixins,
                summary_purpose=class_data.summary.purpose,
                summary_key_behaviors=class_data.summary.key_behaviors,
                summary_dependencies=class_data.summary.dependencies,
                summary_usage_context=class_data.summary.usage_context,
                summary_state_management=class_data.summary.state_management,
                summary=flattened_summary
            )
            # 3. Decorators (list of Decorator objects)
            if class_data.decorators:
                decorator_query = """
                    MATCH (cls:CLASS {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $class_name})
                    
                    MERGE (dec:CLASS_DECORATOR {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        name: $decorator_name
                    })
                    ON CREATE SET
                        dec.arguments = $arguments,
                        dec.purpose = $purpose,
                        dec.created_at = timestamp()
                    
                    MERGE (cls)-[:HAS_CLASS_DECORATOR]->(dec)
                """

                for deco in class_data.decorators:
                    self.execute_query(
                        decorator_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        class_name=class_data.name,
                        decorator_name=deco.name,
                        arguments=deco.arguments,
                        purpose=deco.purpose
                    )

            # 4. Generic parameters (list of GenericParameter)
            if class_data.generic_params:
                generic_param_query = """
                    MATCH (cls:CLASS {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $class_name})
                    
                    MERGE (gp:GENERIC_PARAM {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        name: $param_name
                    })
                    ON CREATE SET
                        gp.constraints = $constraints,
                        gp.default_type = $default_type,
                        gp.created_at = timestamp()
                    
                    MERGE (cls)-[:HAS_GENERIC_PARAM]->(gp)
                """

                for gp in class_data.generic_params:
                    self.execute_query(
                        generic_param_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        class_name=class_data.name,
                        param_name=gp.name,
                        constraints=gp.constraints,
                        default_type=gp.default_type
                    )

            # 5. Properties (list of Property)
            if class_data.properties:
                property_query = """
                    MATCH (cls:CLASS {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $class_name})
                    
                    MERGE (prop:CLASS_PROPERTY {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        name: $prop_name
                    })
                    ON CREATE SET
                        prop.type = $prop_type,
                        prop.visibility = $prop_visibility,
                        prop.kind = $prop_kind,
                        prop.initial_value = $initial_value,
                        // Flatten the list of decorator strings directly on prop node
                        prop.decorators = $decorators,
                        prop.created_at = timestamp()
                    
                    MERGE (cls)-[:HAS_PROPERTY]->(prop)
                """

                for prop in class_data.properties:
                    self.execute_query(
                        property_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        class_name=class_data.name,
                        prop_name=prop.name,
                        prop_type=prop.type,
                        prop_visibility=prop.visibility.value,
                        prop_kind=prop.kind.value,
                        initial_value=prop.initial_value,
                        decorators=prop.decorators
                    )

            # 6. Constructor (optional single object)
            if class_data.constructor:
                constructor_query = """
                    MATCH (cls:CLASS {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $class_name})
                    
                    MERGE (ctor:CONSTRUCTOR {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name
                    })
                    ON CREATE SET
                        ctor.initialization = $initialization,
                        ctor.pre_initialization = $pre_init,
                        ctor.post_initialization = $post_init,
                        ctor.injected_dependencies = $injected_dependencies,
                        ctor.superclass_call = $superclass_call,
                        ctor.created_at = timestamp()
                    
                    MERGE (cls)-[:HAS_CONSTRUCTOR]->(ctor)
                    
                    RETURN ctor
                """

                constructor_data = class_data.constructor
                self.execute_query(
                    constructor_query,
                    file_path=file_path,
                    company_id=company_id,
                    repo_id=repo_id,
                    branch_id=branch_id,
                    class_name=class_data.name,
                    initialization=constructor_data.initialization,
                    pre_init=constructor_data.pre_initialization,
                    post_init=constructor_data.post_initialization,
                    injected_dependencies=constructor_data.injected_dependencies,
                    superclass_call=constructor_data.superclass_call
                )

                # 6a. Constructor Parameters
                if class_data.constructor and constructor_data.parameters:
                    constructor_param_query = """
                        MATCH (ctor:CONSTRUCTOR {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name})
                        
                        MERGE (cp:CONSTRUCTOR_PARAMETER {
                            company_id: $company_id,
                            repo_id: $repo_id,
                            branch_id: $branch_id,
                            file_path: $file_path,
                            class_name: $class_name,
                            name: $param_name
                        })
                        ON CREATE SET
                            cp.type = $param_type,
                            cp.optional = $optional,
                            cp.default_value = $default_value,
                            cp.description = $description,
                            cp.created_at = timestamp()
                        
                        MERGE (ctor)-[:HAS_PARAMETER]->(cp)
                    """

                    for cp in constructor_data.parameters:
                        self.execute_query(
                            constructor_param_query,
                            file_path=file_path,
                            company_id=company_id,
                            repo_id=repo_id,
                            branch_id=branch_id,
                            class_name=class_data.name,
                            param_name=cp.name,
                            param_type=cp.type,
                            optional=cp.optional,
                            default_value=cp.default_value,
                            description=cp.description
                        )

            # 7. Methods (list of Method)
            if class_data.methods:
                # Method creation
                method_query = """
                    MATCH (cls:CLASS {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, name: $class_name})
                    
                    MERGE (m:METHOD {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        name: $method_name
                    })
                    ON CREATE SET
                        m.kind = $method_kind,
                        m.visibility = $method_visibility,
                        m.pure = $pure,
                        m.summary = $summary,
                        m.created_at = timestamp()
                    
                    MERGE (cls)-[:HAS_METHOD]->(m)
                    
                    RETURN m
                """

                # Method decorator creation
                method_decorator_query = """
                    MATCH (m:METHOD {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        name: $method_name
                    })
                    
                    MERGE (dec:METHOD_DECORATOR {
                        company_id: $company_id, 
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        method_name: $method_name,
                        name: $dec_name
                    })
                    ON CREATE SET
                        dec.arguments = $arguments,
                        dec.purpose = $purpose,
                        dec.created_at = timestamp()
                    
                    MERGE (m)-[:HAS_METHOD_DECORATOR]->(dec)
                """

                # Method signature creation
                signature_query = """
                    MATCH (m:METHOD {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name, name: $method_name})
                    
                    MERGE (sig:METHOD_SIGNATURE {
                        company_id: $company_id, 
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        method_name: $method_name
                    })
                    ON CREATE SET
                        sig.return_type = $return_type,
                        sig.throws = $throws,
                        sig.created_at = timestamp()
                    
                    MERGE (m)-[:HAS_SIGNATURE]->(sig)
                    
                    RETURN sig
                """

                # Method parameter creation
                parameter_query = """
                    MATCH (sig:METHOD_SIGNATURE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name, method_name: $method_name})
                    
                    MERGE (param:METHOD_PARAMETER {
                        company_id: $company_id, 
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        method_name: $method_name,
                        name: $param_name
                    })
                    ON CREATE SET
                        param.type = $param_type,
                        param.optional = $optional,
                        param.default_value = $default_value,
                        param.rest_parameter = $rest_parameter,
                        param.description = $description,
                        param.created_at = timestamp()
                    
                    MERGE (sig)-[:HAS_PARAMETER]->(param)
                """

                # Steps creation
                step_query = """
                    MATCH (m:METHOD {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name, name: $method_name})
                    
                    MERGE (st:STEP {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $branch_id,
                        file_path: $file_path,
                        class_name: $class_name,
                        method_name: $method_name,
                        step_id: $step_id
                    })
                    ON CREATE SET
                        st.type = $type,
                        st.description = $description,
                        st.is_async = $is_async,
                        st.critical_section = $critical_section,
                        st.resources = $resources,
                        st.created_at = timestamp()
                    
                    MERGE (m)-[:HAS_STEP]->(st)
                    
                    RETURN st
                """

                # Step dependency
                depends_on_query = """
                    MATCH (s1:STEP {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name, method_name: $method_name, step_id: $step1_id})
                    MATCH (s2:STEP {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name, method_name: $method_name, step_id: $step2_id})
                    MERGE (s1)-[:DEPENDS_ON]->(s2)
                """

                for method in class_data.methods:
                    # 7a. Create the METHOD node
                    self.execute_query(
                        method_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        class_name=class_data.name,
                        method_name=method.name,
                        method_kind=method.kind.value,
                        method_visibility=method.visibility.value,
                        pure=method.pure,
                        summary=method.summary
                    )

                    # 7b. Method Decorators
                    for dec in method.decorators:
                        self.execute_query(
                            method_decorator_query,
                            file_path=file_path,
                            company_id=company_id,
                            repo_id=repo_id,
                            branch_id=branch_id,
                            class_name=class_data.name,
                            method_name=method.name,
                            dec_name=dec.name,
                            arguments=dec.arguments,
                            purpose=dec.purpose
                        )

                    # 7c. Method Signature
                    signature_data = method.signature
                    self.execute_query(
                        signature_query,
                        file_path=file_path,
                        company_id=company_id,
                        repo_id=repo_id,
                        branch_id=branch_id,
                        class_name=class_data.name,
                        method_name=method.name,
                        return_type=signature_data.return_type,
                        throws=signature_data.throws
                    )

                    # 7d. Signature Parameters
                    for param in signature_data.parameters:
                        self.execute_query(
                            parameter_query,
                            file_path=file_path,
                            company_id=company_id,
                            repo_id=repo_id,
                            branch_id=branch_id,
                            class_name=class_data.name,
                            method_name=method.name,
                            param_name=param.name,
                            param_type=param.type,
                            optional=param.optional,
                            default_value=param.default_value,
                            rest_parameter=param.rest_parameter,
                            description=param.description
                        )

                    # Create all step nodes first
                    created_steps = {}
                    for step in method.steps:
                        record = self.execute_query(
                            step_query,
                            return_single_result=True,
                            file_path=file_path,
                            company_id=company_id,
                            repo_id=repo_id,
                            branch_id=branch_id,
                            class_name=class_data.name,
                            method_name=method.name,
                            step_id=step.id,
                            type=step.type.value,
                            description=step.description,
                            is_async=step.is_async,
                            critical_section=step.critical_section,
                            resources=[r.value for r in (step.resources or [])]
                        )
                        # Keep track of the step node if needed (e.g. for depends_on)
                        created_steps[step.id] = record["st"]

                    # 7f. Step Dependencies
                    for step in method.steps:
                        if step.depends_on:
                            for dep_id in step.depends_on:
                                if dep_id in created_steps:  # only create if both steps exist
                                    self.execute_query(
                                        depends_on_query,
                                        file_path=file_path,
                                        company_id=company_id,
                                        repo_id=repo_id,
                                        branch_id=branch_id,
                                        class_name=class_data.name,
                                        method_name=method.name,
                                        step1_id=step.id,
                                        step2_id=dep_id
                                    )

                    # 7g. Step References
                    for step in method.steps:
                        if step.references:
                            # Step references
                            reference_query = """
                                MATCH (st:STEP {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, file_path: $file_path, class_name: $class_name, method_name: $method_name, step_id: $step_id})
                                MATCH (target)
                                WHERE 
                                    ((target:INTERNAL_IMPORT OR target:EXTERNAL_IMPORT) AND target.name = $ref_name)
                                    OR
                                    ((target:DECLARATION OR target:TYPE_DEFINITION) AND target.name = $ref_name)
                                    OR
                                    // Optionally reference other classes or something else
                                    (target:CLASS AND target.name = $ref_name)
                                MERGE (st)-[:REFERENCES]->(target)
                            """
                            # Process imports
                            if step.references.imports:
                                for imp in step.references.imports:
                                    self.execute_query(
                                        reference_query,
                                        file_path=file_path,
                                        company_id=company_id,
                                        repo_id=repo_id,
                                        branch_id=branch_id,
                                        class_name=class_data.name,
                                        method_name=method.name,
                                        step_id=step.id,
                                        ref_name=imp.source_name
                                    )
                            # Process globals
                            if step.references.globals:
                                for glob in step.references.globals:
                                    self.execute_query(
                                        reference_query,
                                        file_path=file_path,
                                        company_id=company_id,
                                        repo_id=repo_id,
                                        branch_id=branch_id,
                                        class_name=class_data.name,
                                        method_name=method.name,
                                        step_id=step.id,
                                        ref_name=glob.name
                                    )
                            # Process classes
                            if step.references.classes:
                                for cls_ref in step.references.classes:
                                    self.execute_query(
                                        reference_query,
                                        file_path=file_path,
                                        company_id=company_id,
                                        repo_id=repo_id,
                                        branch_id=branch_id,
                                        class_name=class_data.name,
                                        method_name=method.name,
                                        step_id=step.id,
                                        ref_name=cls_ref.name
                                    )

    def graph_file_summary(self, file_path: str, company_id: str, repo_id: str, branch_id: str, summary: Dict[str, Any]):
        """
        Update the FILE node with additional summary information.
        Also creates a flattened summary string combining all fields.

        Args:
            file_path (str): The full path of the file.
            branch_id (str): The branch ID the file belongs to.
            summary (Dict[str, Any]): Dictionary conforming to FileSummary schema.
        """
        summary_data = FileSummary.model_validate(summary)

        # Create flattened summary string
        flattened_summary = summary_data.summary

        query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            SET f.summary = $summary,
                f.updated_at = timestamp()
            RETURN f
        """

        self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            file_path=file_path,
            summary=flattened_summary
        )

    def graph_exports(self, file_path: str, company_id: str, repo_id: str, branch_id: str, exports: Dict[str, Any]):
        """
        Create nodes and relationships for file exports.

        This will:
        - Validate the input against CodeGraphExportsSchema.
        - Create (or merge) an EXPORT node for each export in the list, 
          flattening all simple fields (string, boolean, enum) onto the node itself.
        - Connect each EXPORT node to the FILE node via HAS_EXPORT.
        """
        try:
            exports_model = CodeGraphExportsSchema.model_validate(exports)
        except ValidationError as e:
            raise GraphError(f"Invalid exports data: {e}")

        query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            
            MERGE (e:EXPORT {
                company_id: $company_id,
                repo_id: $repo_id,
                branch_id: $branch_id,
                file_path: $file_path,
                name: $name
            })
            ON CREATE SET 
                e.kind = $kind,
                e.source = $source,
                e.is_default = $is_default,
                e.alias = $alias,
                e.summary = $summary,
                e.created_at = timestamp()
            
            MERGE (f)-[:HAS_EXPORT]->(e)
        """

        for export_item in exports_model.exports:
            self.execute_query(
                query,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                file_path=file_path,
                name=export_item.name,
                kind=export_item.kind.value,
                source=export_item.source,
                is_default=export_item.is_default,
                alias=export_item.alias,
                summary=export_item.summary
            )

    def graph_file_references(self, file_path: str, company_id: str, repo_id: str, branch_id: str, references: Dict[str, Any]):
        """
        Create relationships from a FILE node to other graph nodes it references
        (e.g., existing imports, globals, classes), and flatten any simple
        list-based references (like environment variables) onto the FILE node.

        :param file_path: The full path of the file whose references we're recording
        :param branch_id: The branch ID the file belongs to
        :param references: Dictionary conforming to the References schema
        """
        try:
            references_data = References.model_validate(references)
        except ValidationError as e:
            raise GraphError(f"Invalid references data: {e}")

        # This query attempts to match a target node by name among several possible label sets.
        # If found, we MERGE a REFERENCES relationship from FILE -> target.
        #
        # Customize the list of labels according to the references you want to match
        # (e.g., internal/external imports, declarations, type definitions, classes, etc.).
        reference_query = """
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})

            // Attempt to match an existing node with one of these labels by name
            // Adjust this WHERE clause to suit the reference types you want to link to
            OPTIONAL MATCH (target)
            WHERE (
                (target:INTERNAL_IMPORT OR target:EXTERNAL_IMPORT) AND target.name = $ref_name
                OR (target:DECLARATION OR target:TYPE_DEFINITION) AND target.name = $ref_name
                OR (target:CLASS) AND target.name = $ref_name
            )

            // If no target is found, 'target' will be null, so MERGE does nothing. 
            // If found, MERGE the relationship from file to that node.
            FOREACH (_ IN CASE WHEN target IS NOT NULL THEN [1] ELSE [] END |
                MERGE (f)-[:REFERENCES]->(target)
            )
        """

        # -- Imports
        for imp in references_data.imports:
            self.execute_query(
                reference_query,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                file_path=file_path,
                ref_name=imp.source_name
            )

        # -- Globals
        for g in references_data.globals:
            self.execute_query(
                reference_query,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                file_path=file_path,
                ref_name=g.name
            )

        # -- Classes
        for c in references_data.classes:
            self.execute_query(
                reference_query,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                file_path=file_path,
                ref_name=c.name
            )

    def graph_file_sections(self, file_path: str, company_id: str, repo_id: str, branch_id: str, sections: List[Dict[str, Any]]):
        """
        Create SECTION nodes for each logical section in a 'source-adjacent' file
        and attach them to the FILE node via HAS_SECTION relationships.
        """
        try:
            # Validate the incoming data using your Section schema
            section_objects: List[Section] = []
            for section_data in sections:
                section_objects.append(Section.model_validate(section_data))

            section_query = """
                MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
                
                MERGE (sec:SECTION {
                    company_id: $company_id, 
                    repo_id: $repo_id,
                    branch_id: $branch_id,
                    file_path: $file_path,
                    name: $name
                })
                ON CREATE SET
                    sec.section_type = $section_type,
                    sec.format = $format,
                    sec.content = $content,
                    sec.summary = $summary,
                    sec.created_at = timestamp()
                
                MERGE (f)-[:HAS_SECTION]->(sec)
            """

            for sec in section_objects:
                self.execute_query(
                    section_query,
                    company_id=company_id,
                    repo_id=repo_id,
                    branch_id=branch_id,
                    file_path=file_path,
                    name=sec.name,
                    section_type=sec.section_type.value,  # Convert Enum to string
                    format=sec.format.value,  # Convert Enum to string
                    content=sec.content,
                    summary=sec.summary
                )
        except Exception as e:
            logger.error(f"Error in graph_file_sections: {e}")
            raise

    def __enter__(self):
        return self

    def __exit__(self):
        self.close()

    # Implemented but not needed as Git doesn't allow empty folders
    def mark_empty_folders(self, company_id: str, repo_id: str, branch_id: str):
        """
        Iteratively marks all empty folders in the repository for a specific repo_id, and branch_id using APOC.
        Implemented but not needed for now as Git doesn't allow empty folders.

        An empty folder is defined as:
        - A folder with no FILE children, and
        - Either no FOLDER children or all its FOLDER children already have the default empty summary.

        The default summary is:
        "This folder contains no files and all its folders, if any, are empty"

        Two steps are performed:
        1. Mark leaf folders (those with no children at all) as empty.
        2. Iteratively mark folders whose FOLDER children are all marked as empty.
            This uses apoc.periodic.commit to re-run the update until no more changes occur.
        """
        default_summary = "This folder contains no files and all its folders, if any, are empty"
        batch_size = 1000  # Process folders in batches of 1000

        # -- Step 1: Mark leaf folders using apoc.periodic.iterate --
        # These are folders with no FILE or FOLDER children.
        query_leaf_source = """
            MATCH (f:FOLDER)
            WHERE f.company_id = $company_id
            AND f.repo_id = $repo_id 
            AND f.branch_id = $branch_id
            AND NOT (f)-[:HAS_FILE]->(:FILE)
            AND NOT (f)-[:HAS_FOLDER]->(:FOLDER)
            AND f.summary IS NULL
            RETURN f
        """
        query_leaf_action = "SET f.summary = $default_summary"

        leaf_query = """
            CALL apoc.periodic.iterate(
            $source_query,
            $action_query,
            {batchSize: 1000, params: {default_summary: $default_summary, company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id}}
            ) YIELD batches, total, operations, timeTaken, committedOperations, failedOperations, failedBatches, retries, errorMessages
            RETURN batches, total, operations, timeTaken, committedOperations, failedOperations, failedBatches, retries, errorMessages
        """
        leaf_result = self.execute_query(
            leaf_query,
            return_single_result=True,
            source_query=query_leaf_source,
            action_query=query_leaf_action,
            default_summary=default_summary,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        # -- Step 2: Iteratively update folders whose folder children are already empty --
        # We use apoc.periodic.commit to re-run the update until no further folders match.
        query_iterative = """
            CALL {
                MATCH (f:FOLDER)
                WHERE f.company_id: $company_id, 
                AND f.repo_id = $repo_id 
                AND f.branch_id = $branch_id
                AND f.summary IS NULL 
                AND NOT (f)-[:HAS_FILE]->(:FILE)
                AND (f)-[:HAS_FOLDER]->(:FOLDER)
                AND ALL(child IN [(f)-[:HAS_FOLDER]->(child:FOLDER) | child] 
                        WHERE child.summary = $default_summary)
                RETURN f
                LIMIT $limit
            }
            SET f.summary = $default_summary
            RETURN count(f) AS updatedCount;
        """
        iterative_query = """
            CALL apoc.periodic.commit(
            $iterative_query,
            {default_summary: $default_summary, company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, limit: $batch_size}
            ) YIELD executions, updates
            RETURN executions, updates
        """
        iterative_result = self.execute_query(
            query=iterative_query,
            return_single_result=True,
            iterative_query=query_iterative,
            default_summary=default_summary,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            batch_size=batch_size
        )

        return {
            "leafInfo": dict(leaf_result),
            "iterativeInfo": dict(iterative_result)
        }

    def get_folder_children_summaries(self, company_id: str, repo_id: str, branch_id: str) -> List[Dict[str, Any]]:
        """
            Returns a list of dictionaries containing a folder's path and a list of its direct children (files and folders)
            that already have summaries. Only folders that:
            - Do not already have a summary, and
            - Have direct children (FILE or FOLDER) all with summaries,
            are returned.

            The returned structure is:
            {
                'folder_path': <folder path>,
                'children': [
                    { 'path': <child path>, 'summary': <child summary>, 'type': 'file' or 'folder' },
                    ...
                ]
            }

            Debugging

            MATCH (folder:FOLDER {company_id: "COMPANY_ID", repo_id: "REPO_ID", branch_id: "BRANCH_ID"})
            WHERE folder.summary IS NULL
            WITH folder,
                // Check if any direct subfolders lack summaries
                EXISTS {
                    MATCH (folder)-[:HAS_FOLDER]->(subfolder:FOLDER)
                    WHERE subfolder.summary IS NULL
                } AS has_unsummarized_subfolder,

                // Check if any direct files lack summaries
                EXISTS {
                    MATCH (folder)-[:HAS_FILE]->(file:FILE)
                    WHERE file.summary IS NULL
                } AS has_unsummarized_file,

                // For root folder: check if any non-root folders lack summaries
                CASE WHEN folder.path = "" THEN
                    EXISTS {
                        MATCH (other:FOLDER {company_id: "COMPANY_ID", repo_id: "REPO_ID", branch_id: "BRANCH_ID"})
                        WHERE other.path <> "" AND other.summary IS NULL
                    }
                ELSE false END AS root_blocked_by_other_folders,

                // Get child counts
                SIZE([(folder)-[:HAS_FOLDER]->(sf) | sf]) AS subfolder_count,
                SIZE([(folder)-[:HAS_FILE]->(f) | f]) AS file_count

            RETURN 
                folder.path AS path,
                has_unsummarized_subfolder,
                has_unsummarized_file,
                root_blocked_by_other_folders,
                subfolder_count,
                file_count,
                subfolder_count + file_count AS total_children,
                CASE 
                    WHEN has_unsummarized_subfolder THEN "Blocked: Has unsummarized subfolder(s)"
                    WHEN has_unsummarized_file THEN "Blocked: Has unsummarized file(s)"
                    WHEN folder.path = "" AND root_blocked_by_other_folders THEN "Blocked: Root must wait for other folders"
                    WHEN subfolder_count + file_count = 0 AND folder.path <> "" THEN "Ready but has no children"
                    ELSE "Ready for summarization"
                END AS status
            ORDER BY 
                // Process leaf folders first, then work up to root
                CASE WHEN folder.path = "" THEN 1 ELSE 0 END,
                folder.path

        """
        query = """
            MATCH (folder:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            WHERE folder.summary IS NULL
            AND NOT EXISTS {
                MATCH (folder)-[:HAS_FOLDER]->(subfolder:FOLDER)
                WHERE subfolder.summary IS NULL
            }
            AND NOT EXISTS {
                MATCH (folder)-[:HAS_FILE]->(file:FILE)
                WHERE file.summary IS NULL
            }
            AND (
                folder.path <> "" OR 
                NOT EXISTS {
                    MATCH (other:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
                    WHERE other.path <> "" AND other.summary IS NULL
                }
            )
            WITH folder,
                [(folder)-[:HAS_FILE]->(f:FILE) | {
                    path: f.path,
                    summary: f.summary,
                    type: 'file'
                }] AS fileChildren,
                [(folder)-[:HAS_FOLDER]->(sf:FOLDER) | {
                    path: sf.path,
                    summary: sf.summary,
                    type: 'folder'
                }] AS folderChildren
            WITH folder, fileChildren + folderChildren AS children
            WHERE size(children) > 0
            RETURN { folder_path: folder.path, children: children } AS result
            ORDER BY folder.path
        """

        result = self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        return [record["result"] for record in result]

    def check_root_folder_summary(self, company_id: str, repo_id: str, branch_id: str) -> bool:
        """
        Checks if the root folder (indicated by an empty string for its path)
        has been summarized.

        Returns:
            True if the root folder has a summary, False otherwise.
        """
        query = """
            MATCH (root:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: ""})
            RETURN root.summary IS NOT NULL AS has_summary
        """

        record = self.execute_query(
            query,
            return_single_result=True,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )
        return record["has_summary"] if record else False

    def set_folder_summary(self, company_id: str, repo_id: str, branch_id: str, folder_path: str, summary: str):
        """
        Sets the summary for a specific folder.

        Args:
            repo_id: The repository identifier.
            branch_id: The branch identifier.
            folder_path: The path of the folder to update.
            summary: The summary text to set.
        """
        query = """
            MATCH (f:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $folder_path})
            SET f.summary = $summary
        """

        self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            folder_path=folder_path,
            summary=summary
        )

    def get_folder_contents(self, folder_path: str, company_id: str, repo_id: str, branch_id: str) -> Dict[str, Any]:
        """
        Returns a dictionary containing:
        - The specified folder's path and summary
        - A list of all its direct children (files and folders) with summaries for files

        The returned structure is:
        {
            'folder_path': <folder path>,
            'summary': <folder summary>,
            'children': [
                { 'path': <child path>, 'summary': <child summary>, 'type': 'file' or 'folder' },
                ...
            ]
        }
        """
        query = """
            MATCH (folder:FOLDER {path: $folder_path, company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            WITH folder,
                [(folder)-[:HAS_FILE]->(f:FILE) | {
                    path: f.path,
                    summary: f.summary,
                    type: 'file',
                    status: 'UNCHANGED'
                }] AS fileChildren,
                [(folder)-[:HAS_FOLDER]->(sf:FOLDER) | {
                    path: sf.path,
                    type: 'folder'
                }] AS folderChildren
            WITH folder, fileChildren + folderChildren AS children
            RETURN {
                folder_path: folder.path,
                summary: folder.summary,
                children: children
            } AS result
        """

        result = self.execute_query(
            query,
            folder_path=folder_path,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        # Since we're querying by specific path, we expect at most one result
        return result[0]["result"] if result else None

    def get_file_summary(self, file_path: str, company_id: str, repo_id: str, branch_id: str) -> Dict[str, Any]:
        """
        Returns a dictionary containing:
        - The specified file's path and summary

        The returned structure is:
        {
            'file_path': <file path>,
            'summary': <file summary>
        }
        """
        query = """
            MATCH (file:FILE {path: $file_path, company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            RETURN {
                file_path: file.path,
                summary: file.summary
            } AS result
        """

        result = self.execute_query(
            query,
            file_path=file_path,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        # Since we're querying by specific path, we expect at most one result
        return result[0]["result"] if result else None

    def delete_files(self, file_paths: List[str], company_id: str, repo_id: str, branch_id: str):
        """
        Delete specified files and handle parent folders according to requirements:

        1. Finds the files by traversing the folder and file structure
        2. Deletes file nodes and all their children (globals, imports, etc.)
        3. If a file was the only child of a folder, deletes the folder recursively up
        until reaching a parent with other children
        4. For folders with remaining children, clears their summaries recursively up to the root

        Args:
            file_paths: List of file paths to delete
            company_id: Company identifier
            repo_id: Repository identifier
            branch_id: Branch identifier
        """
        for file_path in file_paths:
            # Normalize path for consistency
            file_path = file_path.lstrip('/')

            # Get the folder path before deleting the file
            folder_path = get_parent_folder_path(file_path)

            # Delete the file and all its children
            self.delete_file_and_children(file_path=file_path, company_id=company_id,
                                          repo_id=repo_id, branch_id=branch_id)

            # Handle parent folders (delete or clear summaries)
            if folder_path:
                self.cleanup_parent_folders(folder_path=folder_path, company_id=company_id,
                                            repo_id=repo_id, branch_id=branch_id)
            else:
                self.clear_root_folder_summary(company_id=company_id, repo_id=repo_id, branch_id=branch_id)

    def delete_file_and_children(self, file_path: str, company_id: str, repo_id: str, branch_id: str):
        """
        Delete a file node and all its associated child nodes using APOC.

        This function leverages APOC's subgraph procedures to find all nodes that are 
        reachable from the file node, then deletes them all together. This ensures
        no orphaned nodes remain in the graph regardless of relationship depth.

        Args:
            file_path: Path of the file to delete
            repo_id: Repository identifier
            branch_id: Branch identifier
        """
        query = """
            // Find the file to delete
            MATCH (f:FILE {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $file_path})
            
            // Find and delete the HAS_FILE relationship from parent
            OPTIONAL MATCH (parent:FOLDER)-[r:HAS_FILE]->(f)
            DELETE r
            
            // Use APOC to collect all nodes in the subgraph
            WITH f
            CALL apoc.path.subgraphNodes(f, {
                relationshipFilter: '>', 
                maxLevel: 10
            }) YIELD node
            WHERE node.company_id = $company_id AND node.repo_id = $repo_id AND node.branch_id = $branch_id
            WITH COLLECT(DISTINCT node) AS nodesToDelete
            
            // Delete all collected nodes and their relationships
            UNWIND nodesToDelete AS node
            DETACH DELETE node
            
            RETURN count(nodesToDelete) AS nodes_deleted
        """

        return self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            file_path=file_path
        )

    def delete_folder_and_children(self, folder_path: str, company_id: str, repo_id: str, branch_id: str):
        """
        Delete a folder node and all its associated child nodes (both files and folders).

        This function leverages APOC's subgraph procedures to find all nodes that are 
        reachable from the folder node, then deletes them all together. This ensures
        no orphaned nodes remain in the graph regardless of relationship depth.
        It specifically handles the HAS_FOLDER relationship with the parent folder.

        Browser version
        ------
        // Replace these values with your actual repository and branch IDs
        WITH "eacb916e-a211-41b3-9a69-d7da6bc6a736" AS branch_id,
            "9da1cc07-0809-4c3f-9c54-9278314f12e0" AS repo_id,
            "/path/to/folder" AS folder_path

        // Find the folder to delete
        MATCH (f:FOLDER {company_id: $company_id, repo_id: repo_id, branch_id: branch_id, path: folder_path})

        // Find and delete the HAS_FOLDER relationship from parent
        OPTIONAL MATCH (parent:FOLDER)-[r:HAS_FOLDER]->(f)
        DELETE r

        // Use APOC to collect all nodes in the subgraph (nodes reachable from folder)
        WITH f
        CALL apoc.path.subgraphNodes(f, {relationshipFilter: '>', maxLevel: 10}) YIELD node
        WHERE node.company_id = $company_id AND node.repo_id = $repo_id AND node.branch_id = $branch_id
        WITH COLLECT(DISTINCT node) AS nodesToDelete

        // Delete all the collected nodes and their relationships
        UNWIND nodesToDelete AS node
        DETACH DELETE node

        RETURN count(nodesToDelete) AS nodes_deleted
        ----------------

        Args:
            folder_path: Path of the folder to delete
            repo_id: Repository identifier
            branch_id: Branch identifier
        """
        query = """
            // Find the folder to delete
            MATCH (f:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $folder_path})
            
            // Find and delete the HAS_FOLDER relationship from parent
            OPTIONAL MATCH (parent:FOLDER)-[r:HAS_FOLDER]->(f)
            DELETE r
            
            // Use APOC to collect all nodes in the subgraph (nodes reachable from folder)
            WITH f
            CALL apoc.path.subgraphNodes(f, {relationshipFilter: '>', maxLevel: 10}) YIELD node
            WHERE node.company_id = $company_id AND node.repo_id = $repo_id AND node.branch_id = $branch_id
            WITH COLLECT(DISTINCT node) AS nodesToDelete
            
            // Delete all the collected nodes and their relationships
            UNWIND nodesToDelete AS node
            DETACH DELETE node
        """

        self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            folder_path=folder_path
        )

    def cleanup_parent_folders(self, folder_path: str, company_id: str, repo_id: str, branch_id: str):
        """
        Recursively handle parent folders after file deletion:
        - If folder has no remaining children, delete it and check its parent
        - If folder has other children, clear its summary and continue upward

        Args:
            folder_path: Path of the folder to check
            repo_id: Repository identifier
            branch_id: Branch identifier
        """
        current_path = folder_path

        while True:
            # Check if the current folder exists and if it has children
            check_query = """
                MATCH (f:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $path})
                OPTIONAL MATCH (f)-[:HAS_FILE]->(file:FILE)
                OPTIONAL MATCH (f)-[:HAS_FOLDER]->(subfolder:FOLDER)
                RETURN count(f) > 0 as folder_exists, COUNT(file) + COUNT(subfolder) as child_count
            """

            record = self.execute_query(
                check_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                path=current_path
            )

            if not record or not record['folder_exists']:
                # Folder doesn't exist, move to parent
                if current_path == "":
                    break  # Reached root level, exit

                current_path = get_parent_folder_path(current_path)
                continue

            if record['child_count'] == 0 and current_path != "":
                # Delete this folder as it has no children
                delete_query = """
                    MATCH (f:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $path})
                    DETACH DELETE f
                """

                self.execute_query(
                    delete_query,
                    company_id=company_id,
                    repo_id=repo_id,
                    branch_id=branch_id,
                    path=current_path
                )
            else:
                # This folder has other children, so just clear its summary
                clear_summary_query = """
                    MATCH (f:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $path})
                    SET f.summary = NULL
                """

                self.execute_query(
                    clear_summary_query,
                    company_id=company_id,
                    repo_id=repo_id,
                    branch_id=branch_id,
                    path=current_path
                )

            # Move to the parent folder
            if current_path == "":
                break  # Reached root level, exit

            current_path = get_parent_folder_path(current_path)

        # Process the root folder before exiting
        self.clear_root_folder_summary(company_id=company_id, repo_id=repo_id, branch_id=branch_id)

    def clear_root_folder_summary(self, company_id: str, repo_id: str, branch_id: str):
        root_clear_query = """
            MATCH (f:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id, path: $path})
            SET f.summary = NULL
            """
        self.execute_query(
            root_clear_query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            path=""
        )

    def get_all_folder_paths(
        self,
        company_id: str,
        repo_id: str,
        branch_id: str
    ):
        """
        Retrieve a list of all folder paths for a specific repository and branch.

        Args:
            repo_id: Repository ID (required)
            branch_id: Branch ID (required)

        Returns:
            List of folder paths
        """
        # Build query with required filters
        query = """
            MATCH (f:FOLDER)
            WHERE f.company_id = $company_id
            AND f.repo_id = $repo_id
            AND f.branch_id = $branch_id
            RETURN f.path AS path
            ORDER BY path
        """

        # Execute the query
        results = self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        # Extract the paths from the results
        folder_paths = [record["path"] for record in results]

        return folder_paths

    def get_unsummarized_file_paths(
        self,
        company_id: str,
        repo_id: str,
        branch_id: str
    ):
        """
        Retrieve a list of all unsummarized file paths for a specific repository and branch.

        Args:
            repo_id: Repository ID (required)
            branch_id: Branch ID (required)

        Returns:
            List of file paths for files that don't have summaries
        """
        # Build query with required filters
        query = """
            MATCH (f:FILE)
            WHERE f.company_id = $company_id
            AND f.repo_id = $repo_id
            AND f.branch_id = $branch_id
            AND f.summary IS NULL
            RETURN f.path AS path
            ORDER BY path
        """

        # Execute the query
        results = self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        # Extract the paths from the results
        file_paths = [record["path"] for record in results]

        return file_paths

    def update_batch_status(
        self,
        company_id: str,
        repo_id: str,
        branch_id: str,
        batch_index: int,
        status: BatchStatus
    ):
        """
        Update the status of a specific batch on a branch node.

        Args:
            repo_id: The ID of the repository
            branch_id: The ID of the branch
            batch_index: The index of the batch to update
            status: The status to set (either "IN_PROGRESS" or "COMPLETE")

        Raises:
            GraphError: If the branch node cannot be found
        """
        # Create a property name for this batch status
        batch_status_prop = f"batch_{batch_index}_status"

        query = """
            MATCH (b:BRANCH {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            SET b[$batch_status_prop] = $status
            RETURN b
        """

        record = self.execute_query(
            query,
            return_single_result=True,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            batch_status_prop=batch_status_prop,
            status=status
        )

        if not record:
            raise GraphError(
                f"Failed to update batch status. Branch not found for repo_id={repo_id}, branch_id={branch_id}")

        return record

    def are_other_batches_complete(
        self,
        company_id: str,
        repo_id: str,
        branch_id: str,
        current_batch_index: int,
        total_batches: int
    ):
        """
        Check if all batches other than the current batch are marked as COMPLETE.

        Args:
            repo_id: The ID of the repository
            branch_id: The ID of the branch
            current_batch_index: The index of the current batch (to exclude from check)
            total_batches: The total number of batches

        Returns:
            bool: True if all other batches are complete, False otherwise

        Raises:
            GraphError: If the branch node cannot be found
        """
        # If there's only one batch, then there are no "other" batches to check
        if total_batches <= 1:
            return True

        query = """
            MATCH (b:BRANCH {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            RETURN b
        """

        record = self.execute_query(
            query,
            return_single_result=True,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id
        )

        if not record:
            raise GraphError(
                f"Failed to check batch statuses. Branch not found for repo_id={repo_id}, branch_id={branch_id}")

        branch_node: dict = record["b"]

        # Check the status of all batches except the current one
        for batch_index in range(total_batches):
            if batch_index == current_batch_index:
                continue

            batch_status_prop = f"batch_{batch_index}_status"
            status = branch_node.get(batch_status_prop)

            # If any batch doesn't have a status or is not COMPLETE, return False
            if status != BatchStatus.COMPLETE:
                return False

        # All other batches are COMPLETE
        return True

    def copy_branch(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str
    ):
        """
        Optimized version that copies the branch structure level by level
        using simple queries with LIMIT for batching.
        """

        # Delete existing branch and all its children
        delete_branch_query = """
            MATCH (n) 
            WHERE n.branch_id = $target_branch_id
            DETACH DELETE n
        """

        self.execute_query(
            delete_branch_query,
            target_branch_id=target_branch_id
        )
        logger.info(f"Deleted existing branch {target_branch_id} and all its children")

        # Create the new branch
        create_branch_query = """
            MATCH (r:REPO {company_id: $company_id, repo_id: $repo_id})
            MERGE (new_branch:BRANCH {
                company_id: $company_id, 
                repo_id: $repo_id, 
                branch_id: $target_branch_id
            })
            ON CREATE SET 
                new_branch.created_at = timestamp(),
                new_branch.head_commit_hash = $new_head_commit_hash
            ON MATCH SET 
                new_branch.head_commit_hash = $new_head_commit_hash
            MERGE (r)-[:HAS_BRANCH]->(new_branch)
            RETURN new_branch
        """

        self.execute_query(
            create_branch_query,
            company_id=company_id,
            repo_id=repo_id,
            target_branch_id=target_branch_id,
            new_head_commit_hash=new_head_commit_hash
        )
        logger.info(f"Created new branch {target_branch_id}")

        # Copy root folder
        root_folder_query = """
            MATCH (source_branch:BRANCH {company_id: $company_id, repo_id: $repo_id, branch_id: $source_branch_id})
            MATCH (target_branch:BRANCH {company_id: $company_id, repo_id: $repo_id, branch_id: $target_branch_id})
            MATCH (source_branch)-[:HAS_FOLDER]->(root:FOLDER {path: ""})
            CREATE (new_root:FOLDER {
                company_id: $company_id,
                repo_id: $repo_id,
                branch_id: $target_branch_id,
                path: "",
                name: root.name,
                head_commit_hash: $new_head_commit_hash,
                created_at: timestamp()
            })
            SET new_root.summary = root.summary
            CREATE (target_branch)-[:HAS_FOLDER]->(new_root)
            RETURN new_root
        """

        self.execute_query(
            root_folder_query,
            company_id=company_id,
            repo_id=repo_id,
            source_branch_id=source_branch_id,
            target_branch_id=target_branch_id,
            new_head_commit_hash=new_head_commit_hash
        )
        logger.info("Copied root folder")

        # Copy folders level by level
        self._copy_folders_by_level(company_id, repo_id, source_branch_id, target_branch_id, new_head_commit_hash)

        # Copy files in batches
        self._copy_files_in_batches(company_id, repo_id, source_branch_id, target_branch_id, new_head_commit_hash)

        # Copy file children
        self._copy_file_children(company_id, repo_id, source_branch_id, target_branch_id, new_head_commit_hash)

        # Copy function/class/import children
        self._copy_function_children(company_id, repo_id, source_branch_id, target_branch_id, new_head_commit_hash)
        self._copy_class_children(company_id, repo_id, source_branch_id, target_branch_id, new_head_commit_hash)
        self._copy_import_members(company_id, repo_id, source_branch_id, target_branch_id, new_head_commit_hash)

        # Copy relationships
        # self._copy_inter_node_relationships(company_id, repo_id, source_branch_id, target_branch_id)

        logger.info(f"Branch copy completed from {source_branch_id} to {target_branch_id}")

    def _copy_folders_by_level(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str,
        batch_size=500
    ):
        """Copy folders level by level to maintain hierarchy."""
        level = 0
        max_level = 100  # Safety limit

        while level < max_level:
            # Count folders at this level
            count_query = """
                MATCH path = (root:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $source_branch_id, path: ""})-[:HAS_FOLDER*..%d]->(f:FOLDER)
                WHERE length(path) = $level
                RETURN count(f) as folder_count
            """ % (level + 1)

            result = self.execute_query(
                count_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                level=level + 1
            )

            if not result or result['folder_count'] == 0:
                logger.info(f"No folders found at level {level + 1}")
                break

            # Copy folders at this level in batches
            offset = 0

            while True:
                copy_query = """
                    MATCH path = (root:FOLDER {company_id: $company_id, repo_id: $repo_id, branch_id: $source_branch_id, path: ""})-[:HAS_FOLDER*..%d]->(child:FOLDER)
                    WHERE length(path) = $level
                    WITH child
                    ORDER BY child.path
                    SKIP $offset LIMIT $batch_size
                    MATCH (parent:FOLDER)-[:HAS_FOLDER]->(child)
                    WHERE parent.branch_id = $source_branch_id
                    WITH parent, child
                    MATCH (new_parent:FOLDER {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $target_branch_id,
                        path: parent.path
                    })
                    CREATE (new_child:FOLDER {
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $target_branch_id,
                        path: child.path,
                        name: child.name,
                        head_commit_hash: $new_head_commit_hash,
                        created_at: timestamp()
                    })
                    SET new_child.summary = child.summary
                    CREATE (new_parent)-[:HAS_FOLDER]->(new_child)
                    RETURN count(new_child) as copied
                """ % (level + 1)

                result = self.execute_query(
                    copy_query,
                    return_single_result=True,
                    company_id=company_id,
                    repo_id=repo_id,
                    source_branch_id=source_branch_id,
                    target_branch_id=target_branch_id,
                    new_head_commit_hash=new_head_commit_hash,
                    level=level + 1,
                    offset=offset,
                    batch_size=batch_size
                )

                if not result or result['copied'] == 0:
                    break

                logger.info(f"Copied {result['copied']} folders at level {level + 1}, offset {offset}")
                offset += batch_size

            level += 1

    def _copy_files_in_batches(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str,
        batch_size=500
    ):
        """Copy files in batches."""
        offset = 0

        while True:
            copy_query = """
                MATCH (folder:FOLDER)-[:HAS_FILE]->(file:FILE)
                WHERE folder.company_id = $company_id 
                AND folder.repo_id = $repo_id 
                AND folder.branch_id = $source_branch_id
                WITH folder, file
                ORDER BY file.path
                SKIP $offset LIMIT $batch_size
                MATCH (new_folder:FOLDER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    path: folder.path
                })
                CREATE (new_file:FILE {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    path: file.path,
                    name: file.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_file.summary = file.summary
                CREATE (new_folder)-[:HAS_FILE]->(new_file)
                RETURN count(new_file) as copied
            """

            result = self.execute_query(
                copy_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} files, offset {offset}")
            offset += batch_size

    def _copy_file_children(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str,
        batch_size=500
    ):
        """Copy direct children of files."""

        file_children_types = [
            ("DECLARATION", "HAS_DECLARATION"),
            ("TYPE_DEFINITION", "HAS_TYPE_DEFINITION"),
            ("INTERNAL_IMPORT", "HAS_INTERNAL_IMPORT"),
            ("EXTERNAL_IMPORT", "HAS_EXTERNAL_IMPORT"),
            ("FUNCTION", "HAS_FUNCTION"),
            ("CLASS", "HAS_CLASS"),
            ("EXPORT", "HAS_EXPORT"),
            ("SECTION", "HAS_SECTION")
        ]

        for node_type, rel_type in file_children_types:
            offset = 0

            while True:
                query = f"""
                    MATCH (file:FILE)-[:{rel_type}]->(node:{node_type})
                    WHERE file.company_id = $company_id 
                    AND file.repo_id = $repo_id 
                    AND file.branch_id = $source_branch_id
                    WITH file, node, properties(node) as props
                    ORDER BY file.path, node.name
                    SKIP $offset LIMIT $batch_size
                    MATCH (new_file:FILE {{
                        company_id: $company_id,
                        repo_id: $repo_id,
                        branch_id: $target_branch_id,
                        path: file.path
                    }})
                    CREATE (new_node:{node_type})
                    SET new_node = apoc.map.removeKeys(props, ['branch_id', 'company_id', 'repo_id', 'created_at']),
                        new_node.company_id = $company_id,
                        new_node.repo_id = $repo_id,
                        new_node.branch_id = $target_branch_id,
                        new_node.head_commit_hash = $new_head_commit_hash,
                        new_node.created_at = timestamp()
                    CREATE (new_file)-[:{rel_type}]->(new_node)
                    RETURN count(new_node) as copied
                """

                result = self.execute_query(
                    query,
                    return_single_result=True,
                    company_id=company_id,
                    repo_id=repo_id,
                    source_branch_id=source_branch_id,
                    target_branch_id=target_branch_id,
                    new_head_commit_hash=new_head_commit_hash,
                    offset=offset,
                    batch_size=batch_size
                )

                if not result or result['copied'] == 0:
                    break

                logger.info(f"Copied {result['copied']} {node_type} nodes, offset {offset}")
                offset += batch_size

    def _copy_function_children(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str,
        batch_size=500
    ):
        """
        Copy all child nodes related to functions using simple batched queries.
        """

        # 1. Copy GENERIC_PARAM nodes for functions
        offset = 0

        while True:
            generic_param_query = """
                MATCH (func:FUNCTION)-[:HAS_GENERIC_PARAM]->(gp:GENERIC_PARAM)
                WHERE func.company_id = $company_id 
                AND func.repo_id = $repo_id 
                AND func.branch_id = $source_branch_id
                WITH func, gp
                ORDER BY func.file_path, func.name, gp.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_func:FUNCTION {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    name: func.name
                })
                CREATE (new_gp:GENERIC_PARAM {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    function_name: func.name,
                    name: gp.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_gp.constraints = gp.constraints,
                    new_gp.default_type = gp.default_type
                CREATE (new_func)-[:HAS_GENERIC_PARAM]->(new_gp)
                RETURN count(new_gp) as copied
            """

            result = self.execute_query(
                generic_param_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} function GENERIC_PARAM nodes, offset {offset}")
            offset += batch_size

        # 2. Copy SIGNATURE nodes
        offset = 0
        while True:
            signature_query = """
                MATCH (func:FUNCTION)-[:HAS_SIGNATURE]->(sig:SIGNATURE)
                WHERE func.company_id = $company_id 
                AND func.repo_id = $repo_id 
                AND func.branch_id = $source_branch_id
                WITH func, sig
                ORDER BY func.file_path, func.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_func:FUNCTION {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    name: func.name
                })
                CREATE (new_sig:SIGNATURE {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    function_name: func.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_sig.return_type = sig.return_type,
                    new_sig.throws = sig.throws
                CREATE (new_func)-[:HAS_SIGNATURE]->(new_sig)
                RETURN count(new_sig) as copied
            """

            result = self.execute_query(
                signature_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} SIGNATURE nodes, offset {offset}")
            offset += batch_size

        # 3. Copy FUNCTION_PARAMETER nodes
        offset = 0
        while True:
            parameter_query = """
                MATCH (sig:SIGNATURE)-[:HAS_PARAMETER]->(param:FUNCTION_PARAMETER)
                WHERE sig.company_id = $company_id 
                AND sig.repo_id = $repo_id 
                AND sig.branch_id = $source_branch_id
                WITH sig, param
                ORDER BY sig.file_path, sig.function_name, param.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_sig:SIGNATURE {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: sig.file_path,
                    function_name: sig.function_name
                })
                CREATE (new_param:FUNCTION_PARAMETER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: sig.file_path,
                    function_name: sig.function_name,
                    name: param.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_param.type = param.type,
                    new_param.optional = param.optional,
                    new_param.default_value = param.default_value,
                    new_param.rest_parameter = param.rest_parameter,
                    new_param.description = param.description
                CREATE (new_sig)-[:HAS_PARAMETER]->(new_param)
                RETURN count(new_param) as copied
            """

            result = self.execute_query(
                parameter_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} FUNCTION_PARAMETER nodes, offset {offset}")
            offset += batch_size

        # 4. Copy FUNCTION_DECORATOR nodes
        offset = 0
        while True:
            decorator_query = """
                MATCH (func:FUNCTION)-[:HAS_FUNCTION_DECORATOR]->(dec:FUNCTION_DECORATOR)
                WHERE func.company_id = $company_id 
                AND func.repo_id = $repo_id 
                AND func.branch_id = $source_branch_id
                WITH func, dec
                ORDER BY func.file_path, func.name, dec.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_func:FUNCTION {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    name: func.name
                })
                CREATE (new_dec:FUNCTION_DECORATOR {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    function_name: func.name,
                    name: dec.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_dec.arguments = dec.arguments,
                    new_dec.purpose = dec.purpose
                CREATE (new_func)-[:HAS_FUNCTION_DECORATOR]->(new_dec)
                RETURN count(new_dec) as copied
            """

            result = self.execute_query(
                decorator_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} FUNCTION_DECORATOR nodes, offset {offset}")
            offset += batch_size

        # 5. Copy STEP nodes
        offset = 0
        while True:
            step_query = """
                MATCH (func:FUNCTION)-[:HAS_STEP]->(step:STEP)
                WHERE func.company_id = $company_id 
                AND func.repo_id = $repo_id 
                AND func.branch_id = $source_branch_id
                WITH func, step
                ORDER BY func.file_path, func.name, step.step_id
                SKIP $offset LIMIT $batch_size
                MATCH (new_func:FUNCTION {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    name: func.name
                })
                CREATE (new_step:STEP {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: func.file_path,
                    function_name: func.name,
                    step_id: step.step_id,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_step.type = step.type,
                    new_step.description = step.description,
                    new_step.is_async = step.is_async,
                    new_step.critical_section = step.critical_section,
                    new_step.resources = step.resources
                CREATE (new_func)-[:HAS_STEP]->(new_step)
                RETURN count(new_step) as copied
            """

            result = self.execute_query(
                step_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} function STEP nodes, offset {offset}")
            offset += batch_size

        # 6. Copy DEPENDS_ON relationships between steps
        offset = 0
        while True:
            depends_on_query = """
                MATCH (step1:STEP)-[:DEPENDS_ON]->(step2:STEP)
                WHERE step1.company_id = $company_id 
                AND step1.repo_id = $repo_id 
                AND step1.branch_id = $source_branch_id
                AND step2.branch_id = $source_branch_id
                AND step1.function_name IS NOT NULL  // Ensure it's a function step
                WITH step1, step2
                ORDER BY step1.file_path, step1.function_name, step1.step_id, step2.step_id
                SKIP $offset LIMIT $batch_size
                MATCH (new_step1:STEP {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: step1.file_path,
                    function_name: step1.function_name,
                    step_id: step1.step_id
                })
                MATCH (new_step2:STEP {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: step2.file_path,
                    function_name: step2.function_name,
                    step_id: step2.step_id
                })
                MERGE (new_step1)-[:DEPENDS_ON]->(new_step2)
                RETURN count(*) as copied
            """

            result = self.execute_query(
                depends_on_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} DEPENDS_ON relationships for function steps, offset {offset}")
            offset += batch_size

    def _copy_class_children(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str,
        batch_size=500
    ):
        """
        Copy all child nodes related to classes using simple batched queries.
        """

        # 1. Copy GENERIC_PARAM nodes for classes
        offset = 0
        while True:
            generic_param_query = """
                MATCH (cls:CLASS)-[:HAS_GENERIC_PARAM]->(gp:GENERIC_PARAM)
                WHERE cls.company_id = $company_id 
                AND cls.repo_id = $repo_id 
                AND cls.branch_id = $source_branch_id
                WITH cls, gp
                ORDER BY cls.file_path, cls.name, gp.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_cls:CLASS {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    name: cls.name
                })
                CREATE (new_gp:GENERIC_PARAM {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    class_name: cls.name,
                    name: gp.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_gp.constraints = gp.constraints,
                    new_gp.default_type = gp.default_type
                CREATE (new_cls)-[:HAS_GENERIC_PARAM]->(new_gp)
                RETURN count(new_gp) as copied
            """

            result = self.execute_query(
                generic_param_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} class GENERIC_PARAM nodes, offset {offset}")
            offset += batch_size

        # 2. Copy CLASS_PROPERTY nodes
        offset = 0
        while True:
            property_query = """
                MATCH (cls:CLASS)-[:HAS_PROPERTY]->(prop:CLASS_PROPERTY)
                WHERE cls.company_id = $company_id 
                AND cls.repo_id = $repo_id 
                AND cls.branch_id = $source_branch_id
                WITH cls, prop
                ORDER BY cls.file_path, cls.name, prop.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_cls:CLASS {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    name: cls.name
                })
                CREATE (new_prop:CLASS_PROPERTY {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    class_name: cls.name,
                    name: prop.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_prop.type = prop.type,
                    new_prop.visibility = prop.visibility,
                    new_prop.kind = prop.kind,
                    new_prop.initial_value = prop.initial_value,
                    new_prop.decorators = prop.decorators
                CREATE (new_cls)-[:HAS_PROPERTY]->(new_prop)
                RETURN count(new_prop) as copied
            """

            result = self.execute_query(
                property_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} CLASS_PROPERTY nodes, offset {offset}")
            offset += batch_size

        # 3. Copy CLASS_DECORATOR nodes
        offset = 0
        while True:
            decorator_query = """
                MATCH (cls:CLASS)-[:HAS_CLASS_DECORATOR]->(dec:CLASS_DECORATOR)
                WHERE cls.company_id = $company_id 
                AND cls.repo_id = $repo_id 
                AND cls.branch_id = $source_branch_id
                WITH cls, dec
                ORDER BY cls.file_path, cls.name, dec.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_cls:CLASS {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    name: cls.name
                })
                CREATE (new_dec:CLASS_DECORATOR {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    class_name: cls.name,
                    name: dec.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_dec.arguments = dec.arguments,
                    new_dec.purpose = dec.purpose
                CREATE (new_cls)-[:HAS_CLASS_DECORATOR]->(new_dec)
                RETURN count(new_dec) as copied
            """

            result = self.execute_query(
                decorator_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} CLASS_DECORATOR nodes, offset {offset}")
            offset += batch_size

        # 4. Copy CONSTRUCTOR nodes
        offset = 0
        while True:
            constructor_query = """
                MATCH (cls:CLASS)-[:HAS_CONSTRUCTOR]->(ctor:CONSTRUCTOR)
                WHERE cls.company_id = $company_id 
                AND cls.repo_id = $repo_id 
                AND cls.branch_id = $source_branch_id
                WITH cls, ctor
                ORDER BY cls.file_path, cls.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_cls:CLASS {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    name: cls.name
                })
                CREATE (new_ctor:CONSTRUCTOR {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    class_name: cls.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_ctor.initialization = ctor.initialization,
                    new_ctor.pre_initialization = ctor.pre_initialization,
                    new_ctor.post_initialization = ctor.post_initialization,
                    new_ctor.injected_dependencies = ctor.injected_dependencies,
                    new_ctor.superclass_call = ctor.superclass_call
                CREATE (new_cls)-[:HAS_CONSTRUCTOR]->(new_ctor)
                RETURN count(new_ctor) as copied
            """

            result = self.execute_query(
                constructor_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} CONSTRUCTOR nodes, offset {offset}")
            offset += batch_size

        # 5. Copy CONSTRUCTOR_PARAMETER nodes
        offset = 0
        while True:
            constructor_param_query = """
                MATCH (ctor:CONSTRUCTOR)-[:HAS_PARAMETER]->(param:CONSTRUCTOR_PARAMETER)
                WHERE ctor.company_id = $company_id 
                AND ctor.repo_id = $repo_id 
                AND ctor.branch_id = $source_branch_id
                WITH ctor, param
                ORDER BY ctor.file_path, ctor.class_name, param.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_ctor:CONSTRUCTOR {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: ctor.file_path,
                    class_name: ctor.class_name
                })
                CREATE (new_param:CONSTRUCTOR_PARAMETER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: ctor.file_path,
                    class_name: ctor.class_name,
                    name: param.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_param.type = param.type,
                    new_param.optional = param.optional,
                    new_param.default_value = param.default_value,
                    new_param.description = param.description
                CREATE (new_ctor)-[:HAS_PARAMETER]->(new_param)
                RETURN count(new_param) as copied
            """

            result = self.execute_query(
                constructor_param_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} CONSTRUCTOR_PARAMETER nodes, offset {offset}")
            offset += batch_size

        # 6. Copy METHOD nodes
        offset = 0
        while True:
            method_query = """
                MATCH (cls:CLASS)-[:HAS_METHOD]->(method:METHOD)
                WHERE cls.company_id = $company_id 
                AND cls.repo_id = $repo_id 
                AND cls.branch_id = $source_branch_id
                WITH cls, method
                ORDER BY cls.file_path, cls.name, method.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_cls:CLASS {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    name: cls.name
                })
                CREATE (new_method:METHOD {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: cls.file_path,
                    class_name: cls.name,
                    name: method.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_method.kind = method.kind,
                    new_method.visibility = method.visibility,
                    new_method.pure = method.pure,
                    new_method.summary = method.summary
                CREATE (new_cls)-[:HAS_METHOD]->(new_method)
                RETURN count(new_method) as copied
            """

            result = self.execute_query(
                method_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} METHOD nodes, offset {offset}")
            offset += batch_size

        # 7. Copy METHOD_DECORATOR nodes
        offset = 0
        while True:
            method_decorator_query = """
                MATCH (method:METHOD)-[:HAS_METHOD_DECORATOR]->(dec:METHOD_DECORATOR)
                WHERE method.company_id = $company_id 
                AND method.repo_id = $repo_id 
                AND method.branch_id = $source_branch_id
                WITH method, dec
                ORDER BY method.file_path, method.class_name, method.name, dec.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_method:METHOD {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: method.file_path,
                    class_name: method.class_name,
                    name: method.name
                })
                CREATE (new_dec:METHOD_DECORATOR {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: method.file_path,
                    class_name: method.class_name,
                    method_name: method.name,
                    name: dec.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_dec.arguments = dec.arguments,
                    new_dec.purpose = dec.purpose
                CREATE (new_method)-[:HAS_METHOD_DECORATOR]->(new_dec)
                RETURN count(new_dec) as copied
            """

            result = self.execute_query(
                method_decorator_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} METHOD_DECORATOR nodes, offset {offset}")
            offset += batch_size

        # 8. Copy METHOD_SIGNATURE nodes
        offset = 0
        while True:
            method_signature_query = """
                MATCH (method:METHOD)-[:HAS_SIGNATURE]->(sig:METHOD_SIGNATURE)
                WHERE method.company_id = $company_id 
                AND method.repo_id = $repo_id 
                AND method.branch_id = $source_branch_id
                WITH method, sig
                ORDER BY method.file_path, method.class_name, method.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_method:METHOD {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: method.file_path,
                    class_name: method.class_name,
                    name: method.name
                })
                CREATE (new_sig:METHOD_SIGNATURE {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: method.file_path,
                    class_name: method.class_name,
                    method_name: method.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_sig.return_type = sig.return_type,
                    new_sig.throws = sig.throws
                CREATE (new_method)-[:HAS_SIGNATURE]->(new_sig)
                RETURN count(new_sig) as copied
            """

            result = self.execute_query(
                method_signature_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} METHOD_SIGNATURE nodes, offset {offset}")
            offset += batch_size

        # 9. Copy METHOD_PARAMETER nodes
        offset = 0
        while True:
            method_param_query = """
                MATCH (sig:METHOD_SIGNATURE)-[:HAS_PARAMETER]->(param:METHOD_PARAMETER)
                WHERE sig.company_id = $company_id 
                AND sig.repo_id = $repo_id 
                AND sig.branch_id = $source_branch_id
                WITH sig, param
                ORDER BY sig.file_path, sig.class_name, sig.method_name, param.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_sig:METHOD_SIGNATURE {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: sig.file_path,
                    class_name: sig.class_name,
                    method_name: sig.method_name
                })
                CREATE (new_param:METHOD_PARAMETER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: sig.file_path,
                    class_name: sig.class_name,
                    method_name: sig.method_name,
                    name: param.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_param.type = param.type,
                    new_param.optional = param.optional,
                    new_param.default_value = param.default_value,
                    new_param.rest_parameter = param.rest_parameter,
                    new_param.description = param.description
                CREATE (new_sig)-[:HAS_PARAMETER]->(new_param)
                RETURN count(new_param) as copied
            """

            result = self.execute_query(
                method_param_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} METHOD_PARAMETER nodes, offset {offset}")
            offset += batch_size

        # 10. Copy method STEP nodes
        offset = 0
        while True:
            method_step_query = """
                MATCH (method:METHOD)-[:HAS_STEP]->(step:STEP)
                WHERE method.company_id = $company_id 
                AND method.repo_id = $repo_id 
                AND method.branch_id = $source_branch_id
                WITH method, step
                ORDER BY method.file_path, method.class_name, method.name, step.step_id
                SKIP $offset LIMIT $batch_size
                MATCH (new_method:METHOD {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: method.file_path,
                    class_name: method.class_name,
                    name: method.name
                })
                CREATE (new_step:STEP {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: method.file_path,
                    class_name: method.class_name,
                    method_name: method.name,
                    step_id: step.step_id,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                SET new_step.type = step.type,
                    new_step.description = step.description,
                    new_step.is_async = step.is_async,
                    new_step.critical_section = step.critical_section,
                    new_step.resources = step.resources
                CREATE (new_method)-[:HAS_STEP]->(new_step)
                RETURN count(new_step) as copied
            """

            result = self.execute_query(
                method_step_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} method STEP nodes, offset {offset}")
            offset += batch_size

        # 11. Copy DEPENDS_ON relationships between method steps
        offset = 0
        while True:
            method_depends_query = """
                MATCH (step1:STEP)-[:DEPENDS_ON]->(step2:STEP)
                WHERE step1.company_id = $company_id 
                AND step1.repo_id = $repo_id 
                AND step1.branch_id = $source_branch_id
                AND step2.branch_id = $source_branch_id
                AND step1.method_name IS NOT NULL  // Ensure it's a method step
                WITH step1, step2
                ORDER BY step1.file_path, step1.class_name, step1.method_name, step1.step_id, step2.step_id
                SKIP $offset LIMIT $batch_size
                MATCH (new_step1:STEP {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: step1.file_path,
                    class_name: step1.class_name,
                    method_name: step1.method_name,
                    step_id: step1.step_id
                })
                MATCH (new_step2:STEP {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: step2.file_path,
                    class_name: step2.class_name,
                    method_name: step2.method_name,
                    step_id: step2.step_id
                })
                MERGE (new_step1)-[:DEPENDS_ON]->(new_step2)
                RETURN count(*) as copied
            """

            result = self.execute_query(
                method_depends_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} DEPENDS_ON relationships for method steps, offset {offset}")
            offset += batch_size

    def _copy_import_members(
        self,
        company_id: str,
        repo_id: str,
        source_branch_id: str,
        target_branch_id: str,
        new_head_commit_hash: str,
        batch_size=500
    ):
        """
        Copy all MEMBER and SUB_MEMBER nodes related to imports using simple batched queries.
        """

        # 1. Copy MEMBER nodes from INTERNAL_IMPORT
        offset = 0

        while True:
            internal_member_query = """
                MATCH (imp:INTERNAL_IMPORT)-[:ACCESSES_MEMBER]->(member:MEMBER)
                WHERE imp.company_id = $company_id 
                AND imp.repo_id = $repo_id 
                AND imp.branch_id = $source_branch_id
                WITH imp, member
                ORDER BY imp.file_path, imp.name, member.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_imp:INTERNAL_IMPORT {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: imp.file_path,
                    name: imp.name
                })
                MERGE (new_member:MEMBER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: imp.file_path,
                    name: member.name
                })
                ON CREATE SET 
                    new_member.category = member.category,
                    new_member.usage = member.usage,
                    new_member.access_type = member.access_type,
                    new_member.optional = member.optional,
                    new_member.head_commit_hash = $new_head_commit_hash,
                    new_member.created_at = timestamp()
                MERGE (new_imp)-[:ACCESSES_MEMBER]->(new_member)
                RETURN count(new_member) as copied
            """

            result = self.execute_query(
                internal_member_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} MEMBER nodes from INTERNAL_IMPORT, offset {offset}")
            offset += batch_size

        # 2. Copy MEMBER nodes from EXTERNAL_IMPORT
        offset = 0
        while True:
            external_member_query = """
                MATCH (imp:EXTERNAL_IMPORT)-[:ACCESSES_MEMBER]->(member:MEMBER)
                WHERE imp.company_id = $company_id 
                AND imp.repo_id = $repo_id 
                AND imp.branch_id = $source_branch_id
                WITH imp, member
                ORDER BY imp.file_path, imp.name, member.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_imp:EXTERNAL_IMPORT {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: imp.file_path,
                    name: imp.name
                })
                MERGE (new_member:MEMBER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: imp.file_path,
                    name: member.name
                })
                ON CREATE SET 
                    new_member.category = member.category,
                    new_member.usage = member.usage,
                    new_member.access_type = member.access_type,
                    new_member.optional = member.optional,
                    new_member.head_commit_hash = $new_head_commit_hash,
                    new_member.created_at = timestamp()
                MERGE (new_imp)-[:ACCESSES_MEMBER]->(new_member)
                RETURN count(new_member) as copied
            """

            result = self.execute_query(
                external_member_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} MEMBER nodes from EXTERNAL_IMPORT, offset {offset}")
            offset += batch_size

        # 3. Copy SUB_MEMBER nodes
        offset = 0
        while True:
            sub_member_query = """
                MATCH (member:MEMBER)-[:HAS_SUB_MEMBER]->(sub:SUB_MEMBER)
                WHERE member.company_id = $company_id 
                AND member.repo_id = $repo_id 
                AND member.branch_id = $source_branch_id
                WITH member, sub
                ORDER BY member.file_path, member.name, sub.name
                SKIP $offset LIMIT $batch_size
                MATCH (new_member:MEMBER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: member.file_path,
                    name: member.name
                })
                CREATE (new_sub:SUB_MEMBER {
                    company_id: $company_id,
                    repo_id: $repo_id,
                    branch_id: $target_branch_id,
                    file_path: member.file_path,
                    member_name: member.name,
                    name: sub.name,
                    head_commit_hash: $new_head_commit_hash,
                    created_at: timestamp()
                })
                CREATE (new_member)-[:HAS_SUB_MEMBER]->(new_sub)
                RETURN count(new_sub) as copied
            """

            result = self.execute_query(
                sub_member_query,
                return_single_result=True,
                company_id=company_id,
                repo_id=repo_id,
                source_branch_id=source_branch_id,
                target_branch_id=target_branch_id,
                new_head_commit_hash=new_head_commit_hash,
                offset=offset,
                batch_size=batch_size
            )

            if not result or result['copied'] == 0:
                break

            logger.info(f"Copied {result['copied']} SUB_MEMBER nodes, offset {offset}")
            offset += batch_size

    def get_existing_branches_with_commits(self, company_id: str, repo_id: str) -> List[Dict[str, Any]]:
        """
        Get all existing branches for a repository with their head commit hashes.

        Args:
            company_id: Company identifier
            repo_id: Repository identifier

        Returns:
            List of dictionaries containing branch_id and head_commit_hash
        """
        query = """
            MATCH (b:BRANCH)
            WHERE b.company_id = $company_id
            AND b.repo_id = $repo_id
            RETURN b.branch_id as branch_id, b.head_commit_hash as head_commit_hash
            ORDER BY b.branch_id
        """

        results = self.execute_query(
            query,
            company_id=company_id,
            repo_id=repo_id
        )

        return results

    def is_branch_complete(self, company_id: str, repo_id: str, branch_id: str) -> bool:
        """
        Check if all batches for a branch are marked as COMPLETE.

        Args:
            company_id: Company identifier
            repo_id: Repository identifier
            branch_id: Branch identifier

        Returns:
            True if all batches are complete, False otherwise
        """
        query = """
            MATCH (b:BRANCH {company_id: $company_id, repo_id: $repo_id, branch_id: $branch_id})
            WITH b, keys(b) as props
            // Get all properties that match batch_*_status pattern
            WITH b, [prop IN props WHERE prop STARTS WITH 'batch_' AND prop ENDS WITH '_status'] as batch_props
            // Check if all batch status properties have value 'COMPLETE'
            WITH b, batch_props, 
                [prop IN batch_props WHERE b[prop] = $complete_status] as complete_props
            RETURN 
                size(batch_props) > 0 as has_batches,
                size(batch_props) = size(complete_props) as all_complete,
                size(batch_props) as total_batches,
                size(complete_props) as complete_batches
        """

        result = self.execute_query(
            query,
            return_single_result=True,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            complete_status=BatchStatus.COMPLETE.value
        )

        if not result:
            return False

        # If no batches exist, consider it incomplete
        if not result['has_batches']:
            return False

        return result['all_complete']

    def get_completed_branches(self, company_id: str, repo_id: str) -> List[Dict[str, Any]]:
        """
        Get all branches that have all their batches marked as COMPLETE.

        Args:
            company_id: Company identifier
            repo_id: Repository identifier

        Returns:
            List of completed branches with their branch_id and head_commit_hash
        """
        # First get all branches
        all_branches = self.get_existing_branches_with_commits(company_id, repo_id)

        completed_branches = []
        for branch in all_branches:
            if self.is_branch_complete(company_id, repo_id, branch['branch_id']):
                completed_branches.append(branch)

        logger.info(f"Found {len(completed_branches)} completed branches out of {len(all_branches)} total branches")
        return completed_branches
