from src.process_event import process_payload

payload = {'projectId': 'f90a2869-7915-4be5-b6a0-59aa725978d6', 'jobId': '92ad2fa6-a9fd-4f2f-abab-6a7ceb78d05b',
           'tech_spec_id': '2fbc801d-0fe4-43b7-a4b1-a2da4d14109e', 'org_name': '', 'repo_id': '*********',
           'branch_name': 'main', 'branch_id': '031a8e16-2632-40ea-8e46-f52920544243',
           'head_commit_hash': '6fca26c1c7fb9868182e1961114375e27452b8d5', 'prev_head_commit_hash': '',
           'phase': 'CODE_DOWNLOAD', 'status': 'DONE', 'user_id': '4fc7620e-3009-4abb-a3ef-bc82f456e15d',
           'team_id': '0e930d20-17c0-4301-9a55-49b8694339d8', 'company_id': '4b01c459-254e-4f28-8b5d-923584f0bf82',
           'graph_needs_update': False, 'repo_name': 'CustomerIssue', 'metadata': {'propagate': True}}

if __name__ == '__main__':
    process_payload(payload)
